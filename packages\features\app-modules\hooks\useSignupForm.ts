"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";

import { useToast } from "@meeeetup/ui/toaster";

import { signupSchema } from "@/schema/auth/signup";
import { setAuthRole } from "@/app/(auth)/_actions/signup-action";
import { signupApi } from "@/app/(auth)/_api";
import { useAuthHandlers } from "@/hooks/use-auth-handlers";

// Type definition for the form data
export type SignupFormData = z.infer<typeof signupSchema> & {
  termsAccepted: boolean;
};

interface UseSignupFormProps {
  loginType: "organizer" | "participant";
  nextUrl?: string | null;
  onSignupSuccess?: () => void;
  defaultSignupError?: string;
  onSignupError?: (error?: string) => void;
}

export function useSignupForm({
  loginType,
  nextUrl,
  onSignupSuccess,
  defaultSignupError,
  onSignupError,
}: UseSignupFormProps) {
  const t = useTranslations("SignupPage");
  const commonT = useTranslations("Common");
  const router = useRouter();
  const { toast } = useToast();
  const { handleLoginSuccess: onSuccessDefault, handleLoginError: onErrorDefault } = useAuthHandlers();

  // Create the termsAcceptedSchema with the translated error message
  const termsAcceptedSchema = z.object({
    termsAccepted: z.boolean().refine((val) => val === true, {
      message: t("errors.termsRequired"),
    }),
  });

  // Updated schema using the translated termsAcceptedSchema
  const signupPageSchema = z.intersection(signupSchema, termsAcceptedSchema);

  const {
    register,
    handleSubmit,
    watch,
    control,
    formState: { errors },
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupPageSchema),
    defaultValues: {
      termsAccepted: false,
    },
  });

  const handleSignupSuccess = () => {
    if (onSignupSuccess) {
      onSignupSuccess();
    }
    // Default success behavior (e.g., redirect) can be handled here or by the component
    // For now, let's assume the component handles redirection after signIn
  };

  const handleSignupError = (error?: string) => {
    if (onSignupError) {
      onSignupError(error);
    } else {
      toast({
        title: t("errors.errorTitle"),
        description: error || defaultSignupError || t("errors.signupFailed"),
        variant: "destructive",
      });
    }
  };

  const { mutate: signupMutation, isPending } = useMutation({
    mutationFn: async (data: z.infer<typeof signupSchema>) => {
      const result = await signupApi(data);
      return result;
    },
    onSuccess: async (data, variables) => {
      if (data.success) {
        // Sign in using NextAuth after successful registration
        const signInResult = await signIn("credentials", {
          emailOrPhone: variables.email,
          password: variables.password,
          loginType,
          redirect: false,
        });

        if (signInResult?.error) {
          handleSignupError(t("errors.signInFailed"));
          return;
        }

        // Redirect logic
        if (signInResult && !signInResult.error) {
          let dashboardUrl = "";
          if (loginType === "organizer") {
            dashboardUrl = "/host/dashboard/events";
          } else {
            dashboardUrl = "/participant/dashboard/profile";
          }

          if (nextUrl) {
            router.push(nextUrl);
          } else {
            router.push(dashboardUrl);
          }
          handleSignupSuccess(); // Call success handler
        }
      } else {
        handleSignupError(data.error.details || t("errors.signupFailed"));
      }
    },
    onError: (error: any) => {
      handleSignupError(error?.message || t("errors.signupFailed"));
    },
  });

  const onSubmit = async (formData: SignupFormData) => {
    const { email, password, name, confirmPassword } = formData;
    await setAuthRole(loginType === "organizer" ? "host" : "participant");

    signupMutation({
      email,
      password,
      name,
      confirmPassword,
      isCredentials: true,
      role: loginType === "organizer" ? "host" : "participant",
    });
  };

  return {
    register,
    handleSubmit,
    watch,
    control,
    errors,
    onSubmit,
    isPending,
    t, // Exposing t for use in the component if needed for UI elements not handled by the hook
    commonT,
  };
}
