"use client";

import { But<PERSON> } from "@meeeetup/ui/button";
import { SearchParticipantView } from "../search-participant-view";
import { useFacialReceptionContext } from "../../context/FacialReceptionContext";

export function SearchView() {
  const { dispatch, handleSelectParticipant } = useFacialReceptionContext();

  const handleBackToCamera = () => {
    dispatch({ type: "CLOSE_SEARCH" });
  };

  return (
    <div className="w-full max-w-2xl">
      <div className="bg-white rounded-2xl shadow-sm border border-slate-200/60 p-6 sm:p-8">
        <SearchParticipantView onSelectParticipant={handleSelectParticipant} />
      </div>

      <div className="flex justify-center mt-8">
        <Button onClick={handleBackToCamera} variant="outline" className="px-6 py-3 text-sm font-medium">
          Back to Camera
        </Button>
      </div>
    </div>
  );
}
