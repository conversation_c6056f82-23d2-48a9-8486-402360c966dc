import React from "react";
import { Button } from "@meeeetup/ui/button";

interface AddQuestionButtonProps {
  onClick: () => void;
  label: string;
  className?: string;
}

export const AddQuestionButton: React.FC<AddQuestionButtonProps> = ({ onClick, label, className = "" }) => {
  return (
    <Button
      type="button"
      variant="outline"
      className={`w-full border-dashed py-4 hover:bg-gray-50 ${className}`}
      onClick={(e) => {
        e.preventDefault();
        onClick();
        (e.currentTarget as HTMLButtonElement).blur();
      }}>
      {label}
    </Button>
  );
};

export default AddQuestionButton;
