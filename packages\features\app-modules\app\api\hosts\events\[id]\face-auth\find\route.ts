import { NextRequest } from "next/server";

import { prisma } from "@meeeetup/prisma";

import { responseJson } from "@/lib/response";
import { findFace } from "@/services/aws.service";
import { errors } from "@/lib/errors";
import { findFaceAuth } from "@/services/face-auth.service";
import { withHostRole } from "@/middleware-helpers/with-host-role";
import { zodParse } from "@/lib/zod";
import { fileSchema } from "@/schema/files/file-schema";

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  let session;
  try {
    session = await withHostRole();
    const { id: eventId } = await params;
    const formData = await request.formData();
    const file = formData.get("file") as File;

    const parsedFile = await zodParse(fileSchema, file);

    const response = await findFace({ file: parsedFile });

    if (response.length === 0) {
      throw errors.badRequest("Face not found!");
    }

    const data = []; // Initialize an array for results

    for (const faceMatch of response) {
      const face = faceMatch?.Face;
      if (!face) continue;

      const { ExternalImageId, FaceId, ImageId } = face;
      if (!ExternalImageId || !FaceId || !ImageId) continue;

      // Use a try-catch block for operations related to a single face to allow iteration to continue
      try {
        const faceAuth = await findFaceAuth({
          // externalImageId: ExternalImageId,
          // faceId: FaceId,
          imageId: ImageId,
        });
        // Skip if no matching face authentication record found in our system
        if (!faceAuth) continue;

        const registeredParticipant = await prisma.eventParticipant.findUnique({
          where: {
            event_user_unique: {
              eventId,
              userId: faceAuth.participant.userId,
            },
            status: {
              not: "CANCELLED",
            },
          },
          select: {
            userId: true, // Only need to confirm existence
          },
        });
        if (!registeredParticipant) continue;

        // Fetch the participant's profile using the ID from the faceAuth record
        const participantProfile = await prisma.participantProfile.findUnique({
          where: {
            userId: faceAuth.participantId, // Use participantId from faceAuth
          },
          select: {
            userId: true,
            name: true,
            user: {
              // Need to select the related user to get the email
              select: {
                email: true,
              },
            },
          },
        });

        // Skip if the profile for the authenticated participant is not found or email is missing
        if (!participantProfile || !participantProfile.user?.email) continue;

        // Construct the result object for this valid face
        const profileData = {
          userId: participantProfile.userId,
          name: participantProfile.name,
          email: participantProfile.user.email, // Get email from the related user record
          image: faceAuth.s3Url, // Image URL associated with the face auth record
        };

        data.push(profileData); // Add the profile data to the results array
      } catch (innerError) {
        // Log the error for debugging purposes but continue processing other faces
        console.error(`Error processing face ${FaceId}:`, innerError);
        continue;
      }
    }

    console.log({ data });
    // Note: If no faces are found or validated, 'data' will be an empty array.
    // The original error handling for "Face not found!" for the first face is replaced
    // by potentially returning an empty array if no faces pass all checks.

    return responseJson.success({ data, message: "Face Confirmed", logContext: { session } });
  } catch (error) {
    return responseJson.error({ error: error, message: "Face Confirm failed", logContext: { session } });
  }
}
