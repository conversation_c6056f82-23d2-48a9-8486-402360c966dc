"use client";
import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Settings, Users, Calendar, MapPin, BarChart3, LoaderIcon, FileSpreadsheetIcon } from "lucide-react";

import { EventDetailBanner } from "@meeeetup/ui/event-detail-banner";
import { ParticipantsList } from "@meeeetup/ui/participants-list";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@meeeetup/ui/card";
import { Badge, Button } from "@meeeetup/ui";
import { withDashboardHeader } from "@/app/(dashboard)/_components/with-dashboard-header";
import { ManualCheckInDialog } from "./_components/manual-check-in-dialog/manual-check-in-dialog";
import { AddGuestParticipantDialog } from "./_components/add-guest-participant-dialog";
import { DeleteParticipantDialog } from "./_components/delete-participant-dialog/delete-participant-dialog";
import { QRCodeDialog } from "./_components/qr-code-dialog";
import { EventActionDialog } from "./_components/cancel-event-dialog";
import { EventPageSkeleton } from "./_components/EventPageSkeleton";
import { EventActionButtons } from "./_components/EventActionButtons";
import { useEventData, useAutoCheckoutParticipants, useExportCSV } from "./data-hook";
import { useEventParticipants } from "./_hooks/useEventParticipants";
import { useParticipantActions } from "./_hooks/useParticipantActions";
import { useEventActions } from "./_hooks/useEventActions";
import { useTranslations } from "next-intl";
import { AddAdminDialog } from "./_components/add-admin-dialog";
import { AutoCheckoutDialog } from "./_components/auto-checkout-dialog";

const PAGE_SIZE = 10;

function EventDetailPage() {
  const [qrCodeDialogOpen, setQrCodeDialogOpen] = useState(false);
  const router = useRouter();
  const params = useParams();

  // Ensure id is always a string
  const eventId = typeof params.id === "string" ? params.id : Array.isArray(params.id) ? params.id[0] : "";

  // --- Data Hooks ---
  const {
    eventData,
    isLoading: isEventLoading,
    isError: isEventError,
    error: eventError,
  } = useEventData(eventId);

  const isEventCancelled = eventData?.status === "CANCELLED";

  const {
    participants,
    totalParticipantsCount,
    isParticipantsLoading,
    setCurrentPage,
    handleSortingChange,
    handleFilterChange,
  } = useEventParticipants(eventId as string, PAGE_SIZE);

  // --- Action Hooks ---
  const {
    participantForManualCheckIn,
    manualCheckInDialogOpen,
    setManualCheckInDialogOpen,
    handleManualCheckIn,
    openManualCheckInDialog,
    participantForDelete,
    deleteDialogOpen,
    setDeleteDialogOpen,
    handleParticipantDelete,
    openDeleteDialog,
    handleAddParticipant,
  } = useParticipantActions(eventId as string, eventData);

  const { eventActionDialogOpen, setEventActionDialogOpen, handleEventAction, openEventActionDialog } =
    useEventActions(eventId as string, isEventCancelled);
  const { autoCheckout, isCheckingOut } = useAutoCheckoutParticipants();
  const exportMutation = useExportCSV(eventId as string);

  const eventDetailBannerTranslate = useTranslations("EventDetailBanner");
  const t = useTranslations();
  const eventDetailT = useTranslations("EventDetailPage.hostDashboard");

  // --- Loading and Error States ---
  if (isEventLoading) {
    return <EventPageSkeleton />;
  }

  if (isEventError) {
    return (
      <div className="min-h-[50vh] flex items-center justify-center p-6">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-red-50 rounded-full flex items-center justify-center">
                <Calendar className="h-8 w-8 text-red-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{eventDetailT("errorTitle")}</h3>
                <p className="text-sm text-gray-600">{eventError?.message || eventDetailT("unknownError")}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Format event data for display
  const formattedStartDate = "January 15, 2023 at 2:00 PM";
  const formattedEndDate = "January 15, 2023 at 5:00 PM";

  // --- Render ---
  return (
    <div className="min-h-screen bg-slate-50">
      {/* Hero Banner Section */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto">
          <EventDetailBanner
            backgroundImage={eventData?.bannerImageUrl || undefined}
            onQrCodeClick={() => setQrCodeDialogOpen(true)}
            t={eventDetailBannerTranslate}
            className="h-[300px] md:h-[400px] rounded-none"
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Event Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Event Status Card */}
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-600">{eventDetailT("overview.statusLabel")}</p>
                  <Badge variant={isEventCancelled ? "destructive" : "default"} className="mt-1">
                    {eventData?.status || "Active"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Participants Count Card */}
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-50 rounded-lg">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-600">
                    {eventDetailT("overview.participantsLabel")}
                  </p>
                  <p className="text-2xl font-bold text-slate-900">{totalParticipantsCount || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Card */}
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-orange-50 rounded-lg">
                  <MapPin className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-600">
                    {eventDetailT("overview.locationLabel")}
                  </p>
                  <p className="text-sm font-semibold text-slate-900 truncate">
                    {eventData?.location?.displayName || eventDetailT("overview.noLocationSet")}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>{eventDetailT("eventManagement.title")}</span>
            </CardTitle>
            <CardDescription>{eventDetailT("eventManagement.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <EventActionButtons eventId={eventId as string} router={router} />
            </div>
          </CardContent>
        </Card>

        {/* Participants Management Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>{eventDetailT("participantsManagement.title")}</span>
            </CardTitle>
            <CardDescription>{eventDetailT("participantsManagement.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Admin Controls */}
            <div className="flex flex-wrap gap-3 mb-6 p-4 bg-slate-50 rounded-lg">
              <AddAdminDialog eventId={eventId ?? ""} />
              <AutoCheckoutDialog
                eventId={eventId ?? ""}
                isEventCancelled={isEventCancelled}
                onAutoCheckout={autoCheckout}
                isCheckingOut={isCheckingOut}
                t={t}
              />
              <Button
                onClick={() => exportMutation.mutate()}
                variant={"primary"}
                disabled={exportMutation.isPending}
                className="flex items-center gap-2 text-sm font-medium rounded-full px-4 py-2 transition-all shadow-sm hover:shadow">
                {exportMutation.isPending ? (
                  <>
                    <LoaderIcon className="size-4 animate-spin mr-1" />
                    <span className="sm:inline hidden">
                      {eventDetailT("participantsManagement.exporting")}
                    </span>
                  </>
                ) : (
                  <>
                    <FileSpreadsheetIcon className="size-4" />
                    <span className="sm:inline hidden">
                      {eventDetailT("participantsManagement.exportButton")}
                    </span>
                  </>
                )}
              </Button>
            </div>

            {/* Participants List */}
            <div className="border rounded-lg bg-white p-4">
              <ParticipantsList
                t={(key, options) => t(key, options)}
                data={participants}
                isLoading={isParticipantsLoading}
                totalCount={totalParticipantsCount}
                pageSize={PAGE_SIZE}
                onPageChange={setCurrentPage}
                onSortingChange={handleSortingChange}
                onFilterChange={handleFilterChange}
                onManualLogin={openManualCheckInDialog}
                onRowClick={(participant) => {
                  router.push(`/host/dashboard/events/${eventId as string}/${participant.id}`);
                }}
                onDelete={openDeleteDialog}
                isEventCancelled={isEventCancelled}
                onCancelEvent={openEventActionDialog}
                addParticipantsButton={
                  eventData && !isEventCancelled ? (
                    <AddGuestParticipantDialog onCreate={handleAddParticipant} eventData={eventData} />
                  ) : null
                }
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dialogs */}
      <QRCodeDialog eventId={eventId as string} open={qrCodeDialogOpen} onOpenChange={setQrCodeDialogOpen} />

      {participantForManualCheckIn && (
        <ManualCheckInDialog
          participantName={participantForManualCheckIn.name}
          open={manualCheckInDialogOpen}
          onOpenChange={setManualCheckInDialogOpen}
          onConfirm={() => handleManualCheckIn(participantForManualCheckIn)}
        />
      )}

      {participantForDelete && (
        <DeleteParticipantDialog
          participantName={participantForDelete.name}
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          onConfirm={() => handleParticipantDelete(participantForDelete)}
        />
      )}

      {eventData && (
        <EventActionDialog
          isEventCancelled={isEventCancelled}
          eventName={eventData.name || "this event"}
          open={eventActionDialogOpen}
          onOpenChange={setEventActionDialogOpen}
          onConfirm={handleEventAction}
        />
      )}
    </div>
  );
}

export default withDashboardHeader(EventDetailPage, {
  headerTitle: (props) => {
    const t = useTranslations("EventDetailPage.hostDashboard");
    return t("title");
  },
});
