"use client";

import { FacialReceptionCapture } from "@meeeetup/ui/facial-reception-capture";
import { useFacialReceptionContext } from "../../context/FacialReceptionContext";
import { useTranslations } from "next-intl";

export function CameraView() {
  const { handleCapture } = useFacialReceptionContext();
  const t = useTranslations("FacialReception.CameraView");
  const t2 = useTranslations();

  return (
    <div className="w-full max-w-2xl">
      <div className="bg-white rounded-2xl shadow-sm border border-slate-200/60 p-4 sm:p-6">
        <div className="text-center mb-6">
          <h2 className="text-xl sm:text-2xl font-semibold text-slate-900 mb-2">{t("title")}</h2>
          <p className="text-slate-600">{t("subtitle")}</p>
        </div>

        <div className="rounded-xl overflow-hidden">
          <FacialReceptionCapture
            facingMode="user"
            onCapture={handleCapture}
            className="h-auto max-h-[60vh] sm:max-h-[70vh] w-full"
            t={t2}
          />
        </div>

        <div className="text-center mt-4">
          <p className="text-sm text-slate-500">{t("instructionText")}</p>
        </div>
      </div>
    </div>
  );
}
