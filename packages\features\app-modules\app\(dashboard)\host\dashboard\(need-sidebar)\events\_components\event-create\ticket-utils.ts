import { CreateEventTicketTypesClient } from "../../event-client-types";

export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
  }).format(price);
};

export const getTotalRevenue = (tickets: CreateEventTicketTypesClient[]): number => {
  return tickets.reduce((total, ticket) => total + ticket.price * ticket.quantity, 0);
};

export const getTotalTickets = (tickets: CreateEventTicketTypesClient[]): number => {
  return tickets.reduce((total, ticket) => total + ticket.quantity, 0);
};
