{"HomePage": {"title": "Hello World!", "about": "Go to About Page", "discoverEvents": "Discover Spaces", "searchPlaceholder": "Search spaces...", "viewAll": "View All", "noEvents": {"title": "No Spaces Available", "message": "There are currently no spaces available. Check back later!"}, "testI18n": "Test i18n Setup", "explore": "Explore More Spaces", "welcome": "Welcome to Meeeetup Coworking", "joinCommunity": "Join Our Community", "featuredEvents": "Featured Spaces", "popularCategories": "Popular Categories"}, "BasicQuestionsSection": {"title": "Basic Information", "description": "These fields will be collected from all participants and saved to their profiles. You can only use the predefined questions.", "errorMessage": "Please check your basic questions", "noQuestionsMessage": "No basic questions added yet. Click the button below to add questions.", "required": "Required", "remove": "Remove", "addBasicQuestion": "Add Basic Question", "basicQuestions": {"fullName": "Full Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "bio": "Bio", "sns": "SNS", "gender": "Gender", "workExperience": "Work Experience", "education": "Education"}}, "BasicQuestionDialog": {"title": "Add Basic Question", "description": "Select one of the predefined basic questions to add to your form.", "required": "Required", "optional": "Optional"}, "AboutPage": {"title": "About Us", "home": "Back to Home"}, "FaceRegistration": {"title": "Face Registration", "backButton": "Back", "captureInstruction": "Please position your face in the camera and take a clear photo", "confirmInstruction": "Is this photo clear? Please confirm or retake", "capturedImageAlt": "Captured face", "retakeButton": "Retake Photo", "confirmButton": "Confirm & Register", "registeringMessage": "Registering your face...", "successMessage": "Face registered successfully!", "redirectingMessage": "Redirecting to dashboard...", "errorMessage": "Error registering face", "tryAgainButton": "Try Again"}, "FaceConfirmation": {"title": "Identity Verification", "subtitle": "Please confirm if this is you to access the workspace", "questionPrefix": "Are you", "confirmButton": "Yes, that's me", "denyButton": "This isn't me", "privacyNote": "Your privacy is protected. This verification is secure."}, "Common": {"signIn": "Sign In", "shareWithFriends": "Share with Friends", "createAccount": "Create Account", "getStarted": "Get Started", "seeMore": "See More", "loading": "Loading...", "noSocialLinks": "No SNS links provided", "categories": {"technology": "Technology", "business": "Business", "arts": "Arts & Culture", "sports": "Sports & Fitness", "education": "Education"}, "notProvided": "Not provided", "bio": "Bio", "email": "Email", "phone": "Phone Number", "gender": "Gender", "dateOfBirth": "Date of Birth", "socialLinks": "SNS", "companyName": "Company Name", "description": "Description", "website": "Website", "present": "Present", "cancel": "Cancel", "confirm": "Confirm", "processing": "Processing..."}, "LoginPage": {"title": "Are you an organizer or participant for this space?", "subtitle": "Please select your login type", "loginToAccount": "Login to your account", "asOrganizer": "As Organizer", "asParticipant": "As Participant", "email": "Email Address", "emailPlaceholder": "Enter your email address", "emailRequired": "Email address is required", "password": "Password", "passwordPlaceholder": "Enter your password", "passwordRequired": "Password is required", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginButton": "<PERSON><PERSON>", "loggingIn": "Logging in...", "noAccount": "Don't have an account?", "register": "Register", "unexpectedError": "An unexpected error occurred. Please try again.", "orContinueWith": "or continue with"}, "LoginTypeSelector": {"participant": "Participant", "organizer": "Organizer"}, "SignupPage": {"title": "Create Account", "subtitle": "Join us and start your journey!", "createAccount": "Create Account", "asOrganizer": "as an Organizer", "asParticipant": "as a Participant", "organizer": "Organizer", "participant": "Participant", "email": "Email", "emailPlaceholder": "Enter your email", "name": "Name", "namePlaceholder": "Enter your name", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "termsAgreement": "By signing up, you agree to our ", "termsOfUse": "Terms of Use", "and": " and ", "privacyPolicy": "Privacy Policy", "acknowledgeTerms": "I agree.", "signupButton": "Sign Up", "alreadyHaveAccount": "Already have an account?", "login": "<PERSON><PERSON>", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter your phone number", "orContinueWith": "Or continue with", "errors": {"termsRequired": "You must agree to the terms and conditions", "signupFailed": "Signup failed", "signInFailed": "Sign in after registration failed", "errorTitle": "Error"}}, "HostSidebar": {"eventLists": "Space Management", "facialReception": "Facial Reception", "participantsList": "Participants List", "myProfile": "My Profile", "settings": "Settings"}, "ParticipantSidebar": {"myProfile": "My Profile", "favouriteList": "Favourite List", "games": "Games", "settings": "Settings", "events": "My Spaces"}, "HostProfile": {"aboutSectionTitle": "About", "noSocialLinks": "No SNS links provided", "companyInfoSectionTitle": "Company Information", "companySocialLinksLabel": "Company SNS Links", "noCompanySocialLinks": "No company SNS links provided"}, "AboutSection": {"title": "About", "nameLabel": "Name", "namePlaceholder": "Enter your name", "bioLabel": "Bio", "bioPlaceholder": "Write a short bio about yourself...", "emailLabel": "Email", "emailPlaceholder": "Enter your email", "phoneLabel": "Phone", "phonePlaceholder": "Enter your phone number", "genderLabel": "Gender", "genderPlaceholder": "Select gender", "dateOfBirthLabel": "Date of Birth", "dateOfBirthPlaceholder": "Select date of birth", "socialLinksLabel": "SNS Links", "gender": {"male": "Male", "female": "Female", "other": "Other", "preferNotToSay": "Prefer not to say"}}, "ParticipantProfilePage": {"headerTitle": "Participant Profile", "loadingError": "Failed to load profile data", "retryButton": "Retry", "tabs": {"basicInfo": "Basic Information", "contactInfo": "Contact Information"}, "eventPreview": {"selectPrompt": "Please select a booking to view its preview"}, "upcomingEvents": {"title": "Upcoming Bookings", "noEvents": "No upcoming bookings found", "countText": "You have {count} upcoming bookings"}, "pastEvents": {"title": "Past Bookings", "countText": "You participated in {count} past bookings", "noEvents": "No past bookings found"}, "noEvents": "You haven't booked any spaces yet.", "participantInfo": {"selectPrompt": "Select a booking to view your submitted information"}}, "ParticipantInfoSection": {"personalInformation": {"title": "Personal Information", "fullName": "Full Name"}, "workExperience": {"title": "Work Experience", "current": "Current", "previous": "Previous"}, "education": {"title": "Education", "defaultLabel": "Education {index}"}, "personalInfoTitle": "Personal Information", "workExperienceTitle": "Work Experience", "currentLabel": "Current", "previousLabel": "Previous", "educationTitle": "Education", "universityLabel": "University", "bachelorLabel": "Bachelor"}, "ParticipantEventsPage": {"pageTitle": "My Spaces", "loadingError": "Failed to load booking data", "retryButton": "Retry", "noEvents": "You haven't booked any spaces yet.", "pagination": {"previous": "Previous", "pageInfo": "Page {currentPage} of {totalPages}", "next": "Next"}, "upcomingEvents": {"title": "Upcoming Bookings", "noEvents": "No upcoming bookings found", "countText": "You have {count} upcoming bookings"}, "pastEvents": {"title": "Past Bookings", "countText": "You participated in {count} past bookings", "noEvents": "No past bookings found"}, "eventPreview": {"selectPrompt": "Please select a booking to view its preview"}, "participantInfo": {"selectPrompt": "Select a booking to view your submitted information"}}, "ApplicationForm": {"basicInfo": {"name": {"label": "Application Form Name", "placeholder": "Enter your Application Form Name"}, "description": {"label": "Application Form Description", "placeholder": "Enter your Application Form Description"}}, "additionalQuestions": {"title": "Additional Information", "description": "Add custom questions for participants to provide more information specific to your space.", "errorMessage": "Please check your additional questions"}, "header": {"back": "Back", "title": "Booking Form Information", "description": "Create a Form for the user to fill in their Information for booking"}, "termsAndPrivacy": {"termsOfUse": {"label": "Host's Terms of Use for Users", "placeholder": "Write your Terms of Use in here"}, "privacyPolicy": {"label": "Host's Privacy and Policy for Users", "placeholder": "Write your privacy and policy in here"}}, "footer": {"copyUrl": "Booking Form URL", "copyTooltip": "Copy Booking Form URL", "eventIdNotFound": "Space ID not found", "saveButton": "Save changes", "savingButton": "Saving...", "loadingButton": "Loading..."}}, "EventActionButtons": {"eventDetails": "Space Details", "applicationForm": "Form", "facialReception": "Facial Reception"}, "EventDetailBanner": {"noBannerImage": "No Banner Image", "eventPageUrl": "Space Page URL", "qrCode": "QR Code", "editEventDetails": "Edit Space Details"}, "ParticipantsList": {"searchPlaceholder": "Search By Participants Name", "loading": "Loading...", "noParticipants": "No participants found.", "participantProfile": "Participant Profile", "email": "Email", "eventAppliedDate": "Booking Date", "status": "Status", "facialPhoto": "Facial Photo", "actions": "Actions", "manualCheckIn": "Manual Check In", "cancelEvent": "Cancel Space", "restoreEvent": "Restore Space", "cancelEventDescription": "When Booking is canceled, it'll automatically notify to the participants", "pageInfo": "Page {currentPage} of {totalPages}", "attended": "Attended", "approved": "Absent", "checked_in": "Checked In", "checked_out": "Checked Out", "yes": "Yes", "no": "No"}, "ParticipantsPage": {"headerTitle": "Participants", "searchPlaceholder": "Search By Participants Name", "sortBy": "Sort By", "sortOptions": {"default": "<PERSON><PERSON><PERSON>", "nameAsc": "Name (A-Z)", "nameDesc": "Name (Z-A)", "newestFirst": "Newest First", "oldestFirst": "Oldest First"}, "noParticipantsFound": "No participants found", "tryDifferentSearch": "Try a different search term", "noParticipantsYet": "There are no participants yet", "participantRole": "Participant", "pagination": {"previous": "Previous", "pageInfo": "{currentPage} of {totalPages}", "next": "Next"}}, "CompanyInformationSection": {"title": "Company Information", "companyNameLabel": "Company Name", "companyNamePlaceholder": "Enter company name", "companyEmailLabel": "Email", "companyEmailPlaceholder": "Enter company email", "companyPhoneLabel": "Phone", "companyPhonePlaceholder": "Enter company phone number", "companyWebsiteLabel": "Website", "companyWebsitePlaceholder": "Enter company website URL"}, "EventStatusBadge": {"draft": "Draft", "published": "Published", "cancelled": "Cancelled", "completed": "Completed"}, "ProfileEditPage": {"headerTitle": "Edit Profile", "defaultName": "User", "getPresignedUrlFailed": "Failed to get presigned URL for image upload.", "uploadToStorageFailed": "Failed to upload image to storage.", "profilePictureUpdateSuccess": "Profile picture updated successfully.", "profilePictureUpdateFailed": "Failed to update profile picture.", "profilePictureUpdateError": "An error occurred while updating the profile picture.", "profileUpdateSuccess": "Profile updated successfully.", "profileUpdateFailed": "Failed to update profile.", "profileUpdateError": "An error occurred while updating the profile.", "validationErrorTitle": "Validation Error", "validationErrorDescription": "Please check the form for errors and try again.", "loadingError": "Failed to load profile data. Please try again.", "saveButton": "Save Changes", "savingButton": "Saving...", "socialNetworksAccordionTitle": "SNS Links", "educationAccordionTitle": "Education", "workExperienceAccordionTitle": "Work Experience"}, "Settings": {"title": "Settings", "selectSetting": "Select a setting", "eventSettings": "Space Settings", "changePassword": "Change Password", "paymentSetting": "Payment Setting", "privacySetting": "Privacy Setting", "profilePrivacy": {"title": "Profile Information to private", "description": "All of information will be public as a default, you can choose to private"}, "eventPrivacy": {"title": "Space/Booking Information to private", "description": "All space/booking information will be public as a default, you can choose to private. The mutual bookings will also private"}, "passwordSetting": "Password Setting", "oldPasswordPlaceholder": "Enter Your Old Password", "newPasswordPlaceholder": "Enter Your New Password", "confirmPasswordPlaceholder": "Retype Your New Password", "saveChanges": "Save Changes", "paymentConfiguration": "Payment Configuration", "edit": "Edit", "history": "History", "payoutInfoDescription": "Please enter your payout information based on the country and currency of your space bookings and group subscriptions.", "bankAccountOwnerName": "Bank Account Owner Name", "bankCode": "Bank Code", "branchCode": "Branch Code", "accountType": "Account Type", "accountNumber": "Account Number", "nextPayout": "Next Payout", "payoutDetails": "Payout Details will be available 24 hours after the space has finished. Payout will be processed 5 business days after the space has finished."}, "PersonalDataForm": {"title": "Personal Data", "nameLabel": "Name", "namePlaceholder": "Enter your name", "emailLabel": "Email", "emailPlaceholder": "Enter your email", "phoneLabel": "Phone", "phonePlaceholder": "Enter your phone number", "genderLabel": "Gender", "genderPlaceholder": "Select gender", "dobLabel": "Date of Birth", "dobPlaceholder": "Select your date of birth"}, "OtherInformationForm": {"title": "Other Information", "bioLabel": "About", "bioPlaceholder": "Creative product designer with a passion for crafting innovative, user-centered designs that bridge functionality and aesthetics.", "socialLinksLabel": "SNS Link"}, "SocialNetworkEditForm": {"SNS": "SNS", "No social networks added yet": "No SNS added yet", "Add your first social network": "Add your first SNS", "Add Social Network": "Add SNS", "Platform": "Platform", "Link": "Link", "Add": "Add", "Cancel": "Cancel"}, "EducationEditForm": {"title": "Education", "addEducation": "Add Education", "noEducation": "No education information added yet.", "universityLabel": "University", "universityPlaceholder": "Enter university name", "majorLabel": "Major", "majorPlaceholder": "Enter your major", "degreeLabel": "Degree", "degreePlaceholder": "Enter your degree", "addEducationTitle": "Add Education", "addEducationDescription": "Add your education details"}, "WorkExperienceForm": {"title": "Work Experience", "addButton": "Add Work Experience", "noExperienceMessage": "No work experience added yet.", "positionLabel": "Role and Position", "positionPlaceholder": "Product Designer", "companyNameLabel": "Company Name", "companyNamePlaceholder": "MEEETUP Company", "startDateLabel": "Start Date", "startDatePlaceholder": "Select start date", "endDateLabel": "End Date", "endDatePlaceholder": "Select end date", "selectStartDate": "Select Start Date", "selectEndDate": "Select End Date", "currentWorkLabel": "I currently work here", "websiteLabel": "Company Website", "websitePlaceholder": "https://unmask.vercel.app", "addExperienceTitle": "Add Work Experience", "addExperienceDescription": "Add your work experience details below.", "cancelButton": "Cancel", "addConfirmButton": "Add"}, "EventForm": {"eventName": "Space Name", "eventNamePlaceholder": "Enter Space Name", "eventDescription": "Space Description", "eventDescriptionPlaceholder": "Enter Space Description", "startDate": "Available Start Date and Time", "endDate": "Available End Date and Time", "eventTimezone": "Operating Timezone", "location": "Location", "customLocation": "Custom Location", "customLocationPlaceholder": "Enter a custom location", "useCustomLocation": "Use custom location (when map location isn't available)", "searchLocation": "No locations found. Try a different search or use the custom location option above.", "searchLocationPlaceholder": "Search for a location...", "banner": "Space Photo/Banner", "uploadBanner": "Upload your Space Photo/Banner", "select": "Select", "createEvent": "Add Space", "updateEvent": "Update Space", "cancel": "Cancel", "submitting": "Submitting...", "errorMessages": {"startEndDate": "Please select availability start and end dates", "endAfterStart": "End date must be after start date", "requiredTicket": "At least one booking option/ticket type is required", "nameRequired": "Space name is required", "bannerImage": "Banner image issue"}, "timezones": {"yangon": "Yangon", "japan": "Japan"}}, "EventCreateDialog": {"title": "Add New Space", "description": "Please enter the space information you want to list", "createButton": "Add Space", "submitButton": "Add Space", "cancelButton": "Cancel"}, "EventUpdateDialog": {"title": "Update Space", "description": "Update your space information", "updateButton": "Update Space", "cancelButton": "Cancel"}, "EditEventDialog": {"title": "Edit Space Details", "updateButton": "Update Space", "cancelButton": "Cancel"}, "EventInformation": {"addToGoogleCalendar": "Add to Google Calendar", "descriptionTitle": "Description", "noDescriptionAvailable": "No description available"}, "EventEditor": {"successTitle": "Success", "successDescription": "Space updated successfully", "errorTitle": "Error", "errorDescription": "Failed to update space", "processingErrorDescription": "Failed to process form data", "imageUploadedTitle": "Image uploaded", "imageUploadedDescription": "Banner image uploaded successfully!", "uploadErrorTitle": "Upload Error", "uploadErrorDescription": "Failed to upload banner image: {errorMessage}", "unknownUploadError": "An unknown error occurred during upload.", "uploadUrlError": "Failed to get upload URL"}, "EventList": {"columns": {"eventName": "Space Name", "eventDate": "Availability", "numOfRegister": "Num of Bookings", "appliedGuests": "Applied Guests", "manualRegisterGuests": "Manual Register Guests", "status": "Status", "state": "State"}, "filterPlaceholder": "Filter space names...", "noEventsFound": "No spaces found.", "previousPage": "Previous page", "nextPage": "Next page"}, "AccessPass": {"loading": {"title": "Verifying Your Access", "message": "Please wait while we authenticate your token..."}, "success": {"title": "Access Granted!", "message": "Redirecting you to your destination..."}, "error": {"title": "Verification Failed", "defaultMessage": "Something went wrong.", "redirect": "Redirecting to login page...", "missingParams": "Missing token or identifier.", "unexpectedError": "An unexpected error occurred."}}, "ParticipantEventDetailPage": {"loading": "Loading space details...", "eventNotFound": {"title": "Space Not Found", "message": "Sorry, the space you're looking for doesn't exist or has been removed.", "browseEvents": "Browse Spaces"}, "pageTitle": "Space Details", "tabs": {"eventInformation": "Space Information", "participantsLists": "Participants Lists", "favouriteLists": "Favorites", "games": "Games"}, "games": {"comingSoon": "Games Coming Soon!", "description": "We're working on exciting games and activities for spaces. Check back soon for interactive experiences!", "status": "Under Development"}, "search": {"participants": "Search By Participants Name", "sortBy": "Sort By", "default": "<PERSON><PERSON><PERSON>", "nameAZ": "Name (A-Z)", "nameZA": "Name (Z-A)", "newest": "Newest First", "oldest": "Oldest First"}, "participantsList": {"noParticipants": "No participants found", "tryDifferentSearch": "Try a different search term", "noParticipantsYet": "There are no bookings for this space yet", "pagination": {"previous": "Previous", "next": "Next", "pageInfo": "{currentPage} of {totalPages}"}}, "favoritesList": {"title": "Favorites List", "favoritesDescription": "People you've marked as favorites in this space", "interestsDescription": "People who have shown interest in you in this space", "favoritePeople": "Favourite People", "interestPeople": "Interest People", "searchFavorites": "Search By Favourite People", "searchInterests": "Search By Interest People", "noFavorites": "No favorite people found", "noInterests": "No interest people found", "noFavoritesYet": "You don't have any favorite participants for this space yet", "noInterestsYet": "You don't have any interest participants for this space yet", "showingResults": "Showing {from} to {to} of {total} results"}}, "EventInfoDetails": {"description": "Description", "noDescriptionAvailable": "No description available", "eventLocation": "Space Location", "noLocationCoordinates": "No location coordinates available", "organizer": "Host", "register": "Register"}, "ParticipantInfo": {"tabs": {"basicInfo": "Basic Information", "additionalInfo": "Additional Information"}, "additionalQuestions": {"title": "Additional Questions"}}, "FavouriteListPage": {"headerTitle": "Favourite List", "searchPlaceholder": "Search By Name", "sortBy": "Sort By:", "sortOptions": {"default": "<PERSON><PERSON><PERSON>", "nameAsc": "Name (A-Z)", "nameDesc": "Name (Z-A)", "newest": "Newest First", "oldest": "Oldest First"}, "noFavoritesFound": "No favorites found", "tryDifferentSearch": "Try a different search term", "noFavoritesAdded": "You haven't added any participants to your favorites yet", "pagination": {"previous": "Previous", "next": "Next", "ofTotal": "of"}}, "SideBar": {"createEvent": "+ Create Space", "logout": "Logout", "menuItems": {"Event Lists": "Space Lists", "Facial Reception": "Facial Reception", "Participants List": "Participants List", "My Profile": "My Profile", "Setting": "Setting"}}, "LoginDialog": {"Login Required": "<PERSON><PERSON> Required", "You need to be logged in to register for this space": "You need to be logged in to register for this space.", "googleLoginFailed": "Google login failed", "unexpectedError": "An unexpected error occurred. Please try again.", "Please log in with your account to continue the registration process": "Please log in with your account to continue the registration process.", "Log in": "Log in", "Don't have an account?": "Don't have an account?", "Sign up": "Sign up"}, "ForgotPasswordPage": {"title": "Forgot Your Password?", "subtitle": "Can't remember your password? We've got you covered. Enter your email, and we'll send you a reset link.", "resetPassword": "Reset Password", "enterEmailToReset": "Enter your email to reset your password", "email": "Email", "emailPlaceholder": "Enter your email", "send": "Send", "sending": "Sending...", "rememberPassword": "Remember your password?", "login": "<PERSON><PERSON>", "resetLinkSent": "Reset Link Sent", "resetLinkSentDescription": "If an account exists with this email, you will receive a reset link.", "error": "Error", "unexpectedError": "An unexpected error occurred. Please try again."}, "EventRegistration": {"Event Application Form Information": "Space Booking Form Information", "Please fill form information to know about you": "Please fill form information to know about you", "Read more": "Read more", "Show less": "Show less", "Basic Information": "Basic Information", "Additional Information": "Additional Information", "additionalInfoSectionDescription": "Help us personalize your workspace experience with these optional details", "Select Ticket": "Select Booking", "ticketSelectionDescription": "Choose your booking type to complete your reservation", "legalSectionTitle": "Legal Agreement", "termsAgreementSuffix": ". By proceeding, you acknowledge that you have read and understood these terms.", "statusProcessing": "Processing your booking...", "agreement": {"textBoth": "I have read and agree to the <terms>Terms of Use</terms> and <privacy>Privacy Policy</privacy>.", "textPrivacyOnly": "I have read and agree to the <privacy>Privacy Policy</privacy>.", "textTermsOnly": "I have read and agree to the <terms>Terms of Use</terms>."}, "You must accept the privacy policy and terms of use": "You must accept the privacy policy and terms of use!", "Full Name": "Full Name", "Email Address": "Email Address", "Phone Number": "Phone Number", "Bio": "Bio", "SNS": "SNS", "No social networks added yet": "No SNS added yet", "Add your first social network": "Add your first SNS", "Add Social Network": "Add SNS", "Platform": "Platform", "Link": "Link", "Gender": "Gender", "Select an option": "Select an option", "Male": "Male", "Female": "Female", "Other": "Other", "Work Experience": "Work Experience", "Add Experience": "Add Experience", "No work experience added yet": "No work experience added yet", "Add your first work experience": "Add your first work experience", "Add Work Experience": "Add Work Experience", "Edit Work Experience": "Edit Work Experience", "Position or Title": "Position or Title", "Company Name": "Company Name", "Start Date": "Start Date", "End Date": "End Date", "I currently work here": "I currently work here", "Company Website (optional)": "Company Website (optional)", "Education": "Education", "Add Education": "Add Education", "No education added yet": "No education added yet", "Add your first education": "Add your first education", "University/Institution": "University/Institution", "Degree": "Degree", "Field of Study/Major": "Field of Study/Major", "I am currently studying here": "I am currently studying here", "Description (optional)": "Description (optional)", "Select Ticket Type": "Select Booking Type", "I agree to ": "I agree to ", "privacy policy": "privacy policy", "Privacy Policy": "Privacy Policy", "terms of use": "terms of use", "Terms of Use": "Terms of Use", "Processing": "Processing...", "Confirm": "Confirm", "Add": "Add", "Update": "Update", "Cancel": "Cancel", "Add work experience": "Add work experience", "Add education": "Add education", "At least one work experience entry is required": "At least one work experience entry is required", "At least one education entry is required": "At least one education entry is required", "Edit Education": "Edit Education", "Description": "Description", "Add your social networks": "Add your SNS", "selectTicketTierError": "Please select a ticket tier", "fieldIsRequired": "This field is required", "agreeLegalError": "You must accept the privacy policy and terms of use", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December"}, "TicketDialog": {"title": "Manage Tickets", "description": "Add, edit, or remove ticket types for your space.", "addTicketType": "Add Ticket Type", "noTickets": "No ticket types have been added yet.", "ticketName": "Ticket Name", "ticketPrice": "Price", "ticketQuantity": "Quantity", "actions": "Actions", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "deleteConfirmationTitle": "Confirm Deletion", "deleteConfirmationMessage": "Are you sure you want to delete this ticket type? This action cannot be undone."}, "EventDetailPage": {"autoCheckout": "Auto Checkout Participants", "autoCheckoutConfirmTitle": "Confirm Auto Checkout", "autoCheckoutConfirmDescription": "Are you sure you want to automatically check out all remaining participants for this space? This action cannot be undone.", "checkoutShort": "Checkout", "hostDashboard": {"title": "Space Dashboard", "errorTitle": "Error loading space", "unknownError": "Unknown error occurred", "overview": {"statusLabel": "Status", "participantsLabel": "Bookings", "locationLabel": "Location", "noLocationSet": "No location set"}, "eventManagement": {"title": "Space Management", "description": "Manage your space details, bookings, and settings"}, "participantsManagement": {"title": "Bookings Management", "description": "View and manage space bookings, check-ins, and reservations", "exportButton": "Export CSV", "exporting": "Exporting..."}}, "hero": {"joinMessage": "Book your workspace for an amazing experience", "registerButton": "Registeration member"}, "quickInfo": {"dateTimeLabel": "Date & Time", "locationLabel": "Location", "hostedByLabel": "Hosted by", "onlineEvent": "Virtual Space", "eventHost": "Space Host"}, "aboutSection": {"title": "About This Space", "noDescription": {"title": "No description available", "subtitle": "The host hasn't added space details yet."}}, "locationSection": {"title": "Space Location", "subtitle": "Find your way to the workspace", "noLocation": {"title": "Location Details Coming Soon", "message": "The host will share the exact location details closer to your booking date."}}, "callToAction": {"title": "Ready to Book?", "subtitle": "Don't miss out on this amazing workspace. Reserve your spot now!", "registerButton": "Book This Space"}, "error": {"title": "Space Not Found", "message": "Sorry, the space you're looking for doesn't exist or is no longer available.", "browseEventsButton": "Browse Spaces", "goBackButton": "Go Back"}, "loading": {"loadingText": "Loading..."}}, "FileUploader": {"uploadPlaceholder": "Click or drag file to this area to upload", "uploadHint": "Supported formats: {formats}. Maximum size: {maxSize}.", "uploadButton": "Upload File", "uploading": "Uploading...", "error": {"fileSize": "File size exceeds the limit of {maxSize}.", "fileType": "Invalid file type. Only {formats} are allowed.", "generic": "An error occurred during upload."}, "previewAlt": "Upload preview"}, "AdminManagement": {"title": "Manage Co-Admins", "description": "Add team members as co-admins to help manage this space.", "button": {"text": "Co-Ad<PERSON>", "textMobile": "Admins", "count": "{count, plural, zero {} one {1} other {#}}"}, "addAdmin": {"emailPlaceholder": "Enter email address...", "addButton": "Add Admin", "addingButton": "Adding..."}, "table": {"name": "Name", "email": "Email", "role": "Role", "actions": "Actions", "loading": "Loading...", "noAdmins": "No co-admins found.", "hostRole": "Host", "coAdminRole": "Co-Admin", "removeButton": "Remove", "removingButton": "Removing...", "viewProfile": "View Profile"}, "errors": {"emailRequired": "Email cannot be empty.", "addFailed": "Failed to add admin.", "removeFailed": "Failed to remove admin.", "adminNotFound": "Admin not found for removal.", "emailNotFound": "Admin email not found."}, "adminProfile": {"title": "Admin Profile", "removeButton": "Remove <PERSON>", "removingButton": "Removing...", "confirmRemoval": "Are you sure you want to remove this admin?", "removeDescription": "This action cannot be undone."}}, "HostEventDetailPage": {"title": "Space Dashboard", "loading": "Loading space details...", "error": {"title": "Something went wrong", "message": "Unknown error, please refresh the page", "refreshButton": "Refresh Page"}, "noData": {"title": "No space data found", "message": "The space you're looking for doesn't exist or has been removed.", "goBackButton": "Go Back"}, "stats": {"registeredParticipants": "Active Bookings", "appliedGuests": "Pending Reservations", "eventStatus": "Space Status"}, "eventMeta": {"dateFormat": {"weekday": "short", "month": "short", "day": "numeric"}, "timeFormat": {"hour": "2-digit", "minute": "2-digit"}}, "actions": {"copied": "Copied!", "share": "Share", "qrCode": "QR Code", "editDetails": "Edit Details"}, "status": {"published": "PUBLISHED", "draft": "DRAFT", "cancelled": "CANCELLED", "completed": "COMPLETED"}}, "EventTickets": {"title": "Booking Options", "description": "Create and manage booking types for your space", "stats": {"totalTicketsLabel": "Total Slots", "potentialRevenueLabel": "Potential Revenue"}, "emptyState": {"title": "Create Your First Booking Option", "description": "Start offering desk reservations by creating different access levels for your space. You can always edit them later.", "freeTicketButton": "Free Access", "paidTicketButton": "<PERSON>id <PERSON>"}, "addTicketButtons": {"addFreeTicket": "Add Free Access", "addPaidTicket": "Add Paid Booking"}, "defaultTicketNames": {"paid": "<PERSON>id <PERSON>", "free": "Free Access"}, "actions": {"duplicate": "Duplicate", "remove": "Remove"}, "copyNameSuffix": " (Copy)"}, "TicketAccordionItem": {"statusLabels": {"paid": "Paid", "free": "Free"}, "summaryLabels": {"available": "Available", "price": "Price", "revenue": "Revenue", "free": "Free"}, "formLabels": {"ticketName": "Access Type Name", "required": "*", "price": "Price", "availableQuantity": "Available Slots", "description": "Description"}, "placeholders": {"ticketName": "e.g., Premium Desk, Hot Desk, Meeting Room", "price": "0.00", "quantity": "1", "description": "Describe what's included with this access type..."}, "actions": {"removeTicket": "Remove access type", "duplicate": "Duplicate", "makeFree": "Make Free", "makePaid": "Make Paid", "deleteTicket": "Delete Access Type"}, "currencySymbol": "$"}, "FacialReception": {"registeringFace": "Registering face...", "searchParticipant": {"title": "Find Participant", "subtitle": "Search by name or email to check them in", "searchPlaceholder": "Search by name or email...", "searchingText": "Searching participants...", "participantsFound": "{count, plural, one {# participant found} other {# participants found}}", "noParticipantsFound": {"title": "No participants found", "message": "Try a different search term or check the spelling", "tip": "Tip: Search by first name, last name, or email address"}, "quickActionHint": {"title": "Start typing to search", "message": "Find participants quickly by entering their name or email address"}, "errors": {"eventIdRequired": "Event ID is required", "searchFailed": "Error"}}, "CameraView": {"title": "Facial Recognition Access", "subtitle": "Position yourself in front of the camera for automatic workspace access", "instructionText": "Look directly at the camera for best results"}, "QRCodeDialog": {"title": "Access Granted", "successMessage": "{userName} has been successfully checked in to the workspace", "qrCodeTitle": "Workspace Dashboard", "qrCodeSubtitle": "Scan this QR code to access your workspace dashboard", "continueButton": "Continue Checking In"}, "FaceNotFoundView": {"title": "Face Not Recognized", "description": "We couldn't detect or match this member's face in our system. Try searching manually or capture again.", "searchManuallyButton": "Search Manually", "tryAgainButton": "Try Again"}, "ProcessingView": {"processing": "Processing image…", "pleaseWait": "Please wait while we analyze the captured image."}, "PermissionView": {"processed": "Participant Processed", "title": "Continue facial reception?", "description": "The last participant has been successfully processed. Would you like to continue scanning for the next participant?", "continueButton": "Continue <PERSON>", "finishButton": "Finish & Return"}}, "FormQuestions": {"newQuestion": "New Question", "optionsLabel": "Options:", "addQuestionButton": "Add Question"}, "FacialReceptionCapture": {"switchingCamera": "Switching camera...", "initializingCamera": "Initializing camera...", "cameraAccessRequired": "Camera Access Required", "tryAgain": "Try Again", "tapToCapture": "Tap to capture", "frontCamera": "Front", "backCamera": "Back", "camera": "Camera", "getReady": "Get Ready!", "autoCapturePaused": "Auto-capture is paused.", "resumeOrSkip": "Resume when ready or skip to start immediately.", "autoCaptureWillStartIn": "Auto-capture will start in {seconds} second{seconds, plural, one {} other {s}}.", "adjustCameraSettings": "Adjust your camera settings now.", "resume": "Resume", "pause": "Pause", "startAutoCaptureNow": "Start Auto-Capture Now", "cameraError": "Camera Error", "loading": "Loading...", "cameraReady": "Camera Ready"}}