import React from "react";
import { useTranslations } from "next-intl";

export const RegisterPageSkeleton = () => {
  const t = useTranslations("EventRegistration");

  return (
    <div className="w-full mx-auto max-w-4xl p-4 sm:px-[100px] space-y-8">
      <h1 className="text-2xl font-bold mb-2 text-center">{t("Event Application Form Information")}</h1>
      <p className="text-center text-gray-500">Loading event details...</p>

      {/* Event Details Card Skeleton */}
      <div className="w-full bg-white rounded-lg shadow p-6 animate-pulse">
        <div className="h-40 bg-gray-200 rounded-md mb-4"></div>
        <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/3"></div>
      </div>

      {/* Basic Information Form Skeleton */}
      <div className="w-full bg-white rounded-lg shadow p-6">
        <div className="h-6 bg-gray-200 rounded w-1/3 mb-4 animate-pulse"></div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-10 bg-gray-200 rounded w-full"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Additional Information Form Skeleton */}
      <div className="w-full bg-white rounded-lg shadow p-6">
        <div className="h-6 bg-gray-200 rounded w-1/2 mb-4 animate-pulse"></div>
        <div className="space-y-4">
          {[1, 2].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-10 bg-gray-200 rounded w-full"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Ticket Selection Skeleton */}
      <div className="w-full bg-white rounded-lg shadow p-6">
        <div className="h-6 bg-gray-200 rounded w-1/3 mb-4 animate-pulse"></div>
        <div className="space-y-3">
          {[1, 2].map((i) => (
            <div key={i} className="h-16 bg-gray-200 rounded w-full animate-pulse"></div>
          ))}
        </div>
      </div>

      {/* Button Skeleton */}
      <div className="h-12 bg-gray-200 rounded w-full animate-pulse"></div>
    </div>
  );
};
