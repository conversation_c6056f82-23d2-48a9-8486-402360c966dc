import Link from "next/link";
import { Calendar, MapPin, Users, ArrowRight } from "lucide-react";

interface Event {
  id: string;
  name: string;
  startDatetime: string;
  bannerImageUrl?: string | null;
  location?: {
    displayName?: string;
  } | null;
}

interface EventFeaturedCardProps {
  event: Event;
  index?: number;
  className?: string;
}

export function EventFeaturedCard({ event, index = 0, className = "" }: EventFeaturedCardProps) {
  return (
    <Link
      href={`/events/${event.id}`}
      className={`group relative transform transition-all duration-500 hover:-translate-y-2 focus:outline-none focus:ring-4 focus:ring-blue-500/30 rounded-3xl ${className}`}
      style={{ animationDelay: `${index * 100}ms` }}>
      <div className="bg-white rounded-3xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-500 h-full">
        <div className="relative overflow-hidden">
          <div className="aspect-[4/3] bg-gradient-to-br from-blue-400 to-purple-500">
            {event.bannerImageUrl ? (
              <img
                src={event.bannerImageUrl}
                alt={event.name}
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-400 via-purple-500 to-indigo-600 flex items-center justify-center">
                <Calendar className="w-16 h-16 text-white/80" />
              </div>
            )}
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
            <ArrowRight className="w-4 h-4 text-gray-700" />
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-2 mb-3 text-sm text-blue-600 font-medium">
            <Calendar className="w-4 h-4" />
            {new Date(event.startDatetime).toLocaleDateString()}
          </div>

          <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
            {event.name}
          </h3>

          {event.location?.displayName && (
            <div className="flex items-center gap-2 text-gray-600 mb-4">
              <MapPin className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm line-clamp-1">{event.location.displayName}</span>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-gray-500">
              <Users className="w-4 h-4" />
              <span className="text-sm">Join now</span>
            </div>
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <ArrowRight className="w-4 h-4 text-white" />
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
