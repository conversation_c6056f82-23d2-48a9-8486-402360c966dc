"use client";

import { useFacialReceptionContext } from "../../context/FacialReceptionContext";
import { useTranslations } from "next-intl";

// Simple spinner animation used while image is analysed
const Spinner = ({ className = "" }: { className?: string }) => (
  <svg
    className={`animate-spin ${className}`}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z" />
  </svg>
);

export function ProcessingView() {
  const { state } = useFacialReceptionContext();
  const { capturedImage } = state;
  const t = useTranslations("FacialReception.ProcessingView");

  if (!capturedImage) return null;

  return (
    <div className="w-full max-w-2xl">
      <div className="bg-white rounded-2xl shadow-sm border border-slate-200/60 p-4 sm:p-6">
        {/* Image preview with processing overlay */}
        <div className="relative aspect-video rounded-xl overflow-hidden">
          <img src={capturedImage} alt="Captured face" className="w-full h-full object-cover" />

          {/* Overlay */}
          <div
            className="absolute inset-0 flex flex-col items-center justify-center bg-slate-900/40 backdrop-blur-sm"
            aria-busy="true">
            <Spinner className="w-10 h-10 text-white" />
            <p className="mt-3 text-sm font-medium text-white">{t("processing")}</p>
          </div>
        </div>

        <div className="text-center mt-4">
          <p className="text-sm text-slate-600">{t("pleaseWait")}</p>
        </div>
      </div>
    </div>
  );
}
