import { useState } from "react";
import { GripVertical, Trash2, Edit3 } from "lucide-react";
import { Reorder } from "motion/react";
import { Button } from "@meeeetup/ui/button";
import { AddEditQuestionDialog } from "./add-edit-question-dialog";
import { Badge } from "@meeeetup/ui";
import { type QuestionProps, type QuestionType } from "./question";
import { useTranslations } from "next-intl";
import { AddQuestionButton } from "../_components/AddQuestionButton";

export type QuestionWithId = QuestionProps["value"] & {
  id: string;
  type: QuestionType;
  isRequired: boolean;
  isPrivate: boolean;
};

export interface FormQuestionsProps {
  defaultQuestions?: QuestionWithId[];
  onChange?: (questions: QuestionWithId[]) => void;
  errors?: Record<string, { questionText?: { message: string }; options?: { message: string } }>;
}

export function FormQuestions({ defaultQuestions = [], onChange, errors }: FormQuestionsProps) {
  const [questions, setQuestions] = useState<QuestionWithId[]>(defaultQuestions);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<QuestionWithId | null>(null);
  const t = useTranslations("FormQuestions");

  const updateQuestions = (newQuestions: QuestionWithId[]) => {
    setQuestions(newQuestions);
    onChange?.(newQuestions);
  };

  const handleOpenAddDialog = () => {
    setEditingQuestion(null);
    setIsDialogOpen(true);
  };

  const handleOpenEditDialog = (question: QuestionWithId) => {
    setEditingQuestion(question);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingQuestion(null);
  };

  const handleSaveQuestion = (savedQuestion: QuestionWithId) => {
    let newQuestions;
    if (editingQuestion) {
      newQuestions = questions.map((q) => (q.id === savedQuestion.id ? savedQuestion : q));
    } else {
      const newQuestionWithProperId = {
        ...savedQuestion,
        id: savedQuestion.id.startsWith("temp_") ? crypto.randomUUID() : savedQuestion.id,
      };
      newQuestions = [...questions, newQuestionWithProperId];
    }
    updateQuestions(newQuestions);
    handleCloseDialog();
  };

  const handleDeleteQuestion = (idToDelete: string) => {
    updateQuestions(questions.filter((q) => q.id !== idToDelete));
  };

  const getErrorMessages = (questionId: string): string[] => {
    const questionErrors = errors?.[questionId];
    if (!questionErrors) return [];
    const messages: string[] = [];
    if (questionErrors.questionText?.message) messages.push(questionErrors.questionText.message);
    if (questionErrors.options?.message) messages.push(questionErrors.options.message);
    return messages;
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <Reorder.Group
        axis="y"
        values={questions}
        onReorder={updateQuestions}
        className="space-y-3 sm:space-y-4">
        {questions.map((question) => (
          <Reorder.Item
            key={question.id}
            value={question}
            style={{ position: "relative" }}
            whileDrag={{ zIndex: 10, scale: 1.02 }}
            className={`p-4 border rounded-md ${errors?.[question.id] ? "border-red-500" : "border-gray-200"}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center flex-1">
                <div className="touch-manipulation p-1 mr-2">
                  <GripVertical className="h-5 w-5 text-gray-400 cursor-grab active:cursor-grabbing" />
                </div>
                <span className="font-medium truncate text-sm sm:text-base max-w-[calc(100%-80px)]">
                  {question?.question || t("newQuestion")}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => handleOpenEditDialog(question)}
                  className="touch-manipulation">
                  <Edit3 className="h-5 w-5 text-gray-600" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => handleDeleteQuestion(question.id)}
                  className="touch-manipulation">
                  <Trash2 className="h-5 w-5 text-red-500" />
                </Button>
              </div>
            </div>
            {errors?.[question.id] && (
              <div className="mt-2">
                <ul className="list-disc pl-5 text-red-500 text-xs">
                  {getErrorMessages(question.id).map((message, i) => (
                    <li key={i}>{message}</li>
                  ))}
                </ul>
              </div>
            )}
            {(question.type === "Single Choose Question" || question.type === "Multiple Choose Question") &&
              question.options &&
              question.options.length > 0 && (
                <div className="mt-2 pl-2">
                  <div className="text-xs text-gray-500">
                    {t("optionsLabel")}{" "}
                    {question.options.map((opt) => (
                      <Badge className="mr-1" key={opt.id}>
                        {opt.text}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
          </Reorder.Item>
        ))}
      </Reorder.Group>
      <AddQuestionButton
        onClick={handleOpenAddDialog}
        label={t("addQuestionButton")}
        className="touch-manipulation"
      />
      {isDialogOpen && (
        <AddEditQuestionDialog
          isOpen={isDialogOpen}
          onClose={handleCloseDialog}
          onSave={handleSaveQuestion}
          initialData={editingQuestion}
          questionType={editingQuestion?.type || "Single Choose Question"}
        />
      )}
    </div>
  );
}

export type { QuestionType };
