import * as React from "react";
import { useTranslations } from "next-intl";
import { Button } from "@meeeetup/ui/button";

interface EmptyTicketsStateProps {
  onAddTicket: (isPaid: boolean) => void;
}

export function EmptyTicketsState({ onAddTicket }: EmptyTicketsStateProps) {
  const t = useTranslations("EventTickets");

  return (
    <div className="relative">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-3xl"></div>
      <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl border-2 border-dashed border-gray-200 p-12 text-center">
        <div className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center mb-6">
          <svg className="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"
            />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-3">{t("emptyState.title")}</h3>
        <p className="text-gray-600 mb-8 max-w-md mx-auto">{t("emptyState.description")}</p>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            onClick={() => onAddTicket(false)}
            className="group relative overflow-hidden bg-white border-2 border-gray-200 text-gray-700 hover:border-green-300 hover:bg-green-50 transition-all duration-300">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </div>
              <span className="font-medium">{t("emptyState.freeTicketButton")}</span>
            </div>
          </Button>

          <Button
            onClick={() => onAddTicket(true)}
            className="group relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white transition-all duration-300">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center group-hover:bg-blue-400 transition-colors">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </div>
              <span className="font-medium">{t("emptyState.paidTicketButton")}</span>
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
}
