"use client";

import { signIn } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

import { loginSchema } from "@/schema/auth/login";
import { setAuthRole } from "@/app/(auth)/_actions/signup-action";
import { useAuthHandlers } from "@/hooks/use-auth-handlers";

export type LoginFormData = z.infer<typeof loginSchema>;

interface UseLoginFormProps {
  loginType: "organizer" | "participant";
  nextUrl?: string;
  onLoginSuccess?: () => void;
  defaultLoginError?: string;
  onLoginError?: (error?: string) => void;
}

export function useLoginForm({
  loginType,
  nextUrl,
  onLoginSuccess,
  defaultLoginError,
  onLoginError,
}: UseLoginFormProps) {
  const t = useTranslations("LoginPage"); // Common translations
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const { handleLoginSuccess: onSuccessDefault, handleLoginError: onErrorDefault } = useAuthHandlers();

  const handleLoginSuccess = (resolvedNextUrl?: string) => {
    if (onLoginSuccess) {
      onLoginSuccess();
    }
    onSuccessDefault(resolvedNextUrl || nextUrl);
  };

  const handleLoginError = (error?: string) => {
    if (onLoginError) {
      onLoginError(error);
    } else {
      onErrorDefault(error || defaultLoginError || t("unexpectedError"));
    }
  };

  const { mutate: signInMutation, isPending } = useMutation({
    mutationFn: async (data: LoginFormData) => {
      await setAuthRole(loginType === "organizer" ? "host" : "participant");
      const result = await signIn("credentials", {
        emailOrPhone: data.email,
        password: data.password,
        loginType,
        redirect: false,
      });
      return result;
    },
    onSuccess: (result) => {
      if (result?.error) {
        handleLoginError(result.error);
      } else {
        handleLoginSuccess();
      }
    },
    onError: (error: any) => {
      // Handle generic mutation errors, potentially different from signIn specific errors
      handleLoginError(error?.message || t("unexpectedError"));
    },
  });

  const onSubmit = (data: LoginFormData) => {
    signInMutation(data);
  };

  return {
    register,
    handleSubmit,
    errors,
    signInMutation,
    isPending,
    onSubmit,
    handleLoginSuccess, // Exposing this if direct call needed post-social login
    handleLoginError, // Exposing this if direct call needed post-social login
  };
}
