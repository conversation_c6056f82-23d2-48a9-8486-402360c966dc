import * as React from "react";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@meeeetup/ui/button";

interface AddTicketButtonsProps {
  onAddTicket: (isPaid: boolean) => void;
}

export function AddTicketButtons({ onAddTicket }: AddTicketButtonsProps) {
  const t = useTranslations("EventTickets");

  return (
    <div className="flex flex-col sm:flex-row gap-3">
      <Button
        type="button"
        variant="outline"
        onClick={() => onAddTicket(false)}
        className="flex-1 group relative overflow-hidden border-2 border-dashed border-gray-300 hover:border-green-400 hover:bg-green-50 transition-all duration-300 py-4">
        <div className="flex items-center justify-center gap-3">
          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </div>
          <span className="font-medium text-gray-700 group-hover:text-green-700">
            {t("addTicketButtons.addFreeTicket")}
          </span>
        </div>
      </Button>

      <Button
        type="button"
        onClick={() => onAddTicket(true)}
        className="flex-1 group relative overflow-hidden bg-blue-600 hover:bg-blue-700 transition-all duration-300 py-4">
        <div className="flex items-center justify-center gap-3">
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center group-hover:bg-blue-400 transition-colors">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </div>
          <span className="font-medium text-white">{t("addTicketButtons.addPaidTicket")}</span>
        </div>
      </Button>
    </div>
  );
}
