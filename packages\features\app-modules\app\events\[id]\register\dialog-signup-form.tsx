"use client";

import { useTranslations } from "next-intl";
import { useCallback } from "react";

import { useSignupForm, SignupFormData } from "@/hooks/useSignupForm";
import { SignupFormFields } from "@/components/signup-form-fields";
import { useGoogleLogin } from "@/hooks/useGoogleLogin";
import { useToast } from "@meeeetup/ui/toaster";

interface DialogSignupFormProps {
  nextUrl: string;
  onSignupSuccess?: () => void;
}

export function DialogSignupForm({ nextUrl, onSignupSuccess }: DialogSignupFormProps) {
  const t = useTranslations("SignupPage");
  const commonT = useTranslations("Common");
  const dialogT = useTranslations("LoginDialog");
  const { toast } = useToast();

  const displaySignupError = useCallback(
    (error?: string) => {
      toast({
        title: t("errors.errorTitle"),
        description: error || dialogT("unexpectedError"),
        variant: "destructive",
      });
    },
    [t, dialogT, toast]
  );

  const { register, handleSubmit, control, errors, onSubmit, isPending } = useSignupForm({
    loginType: "participant",
    nextUrl,
    onSignupSuccess,
    onSignupError: displaySignupError,
    defaultSignupError: dialogT("unexpectedError"),
  });

  const { googleLoading, handleGoogleLogin: performGoogleSignup } = useGoogleLogin({
    onLoginError: displaySignupError,
    defaultLoginError: dialogT("googleLoginFailed"),
  });

  const handleGoogleSignup = useCallback(async () => {
    await performGoogleSignup("participant", nextUrl);
  }, [performGoogleSignup, nextUrl]);

  return (
    <SignupFormFields
      hideGoogleButton
      onSubmit={onSubmit}
      register={register}
      handleSubmit={handleSubmit}
      control={control}
      errors={errors}
      isPending={isPending || googleLoading}
      t={t}
      loginType="participant"
      handleGoogleLogin={handleGoogleSignup}
    />
  );
}
