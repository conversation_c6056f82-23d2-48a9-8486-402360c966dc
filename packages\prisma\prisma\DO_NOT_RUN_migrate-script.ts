// @ts-nocheck
import { prisma } from "../src/db-client";
import { PrismaClient as NewPrismaClient } from "../../coworking-prisma/prisma/generated/prisma/client";

async function main() {
  console.log("Starting migration script...");

  // Initialize new Prisma client
  const newPrisma = new NewPrismaClient({
    datasources: {
      db: {
        url: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require",
      },
    },
    transactionOptions: { timeout: 300000 },
  });

  console.log("Creating coworking app...");
  const eventApp = await newPrisma.app.create({
    data: {
      name: "coworking",
    },
  });
  console.log(`Created app with id: ${eventApp.id}`);

  console.log("Connecting app to events...");
  const eventUpdateResult = await newPrisma.event.updateMany({
    where: {
      appId: null,
    },
    data: { appId: eventApp.id },
  });
  console.log(`Updated ${eventUpdateResult.count} events`);

  console.log("Connecting app to policies and terms...");
  const policyUpdateResult = await newPrisma.policyAndTerms.updateMany({
    where: {
      appId: null,
    },
    data: { appId: eventApp.id },
  });
  console.log(`Updated ${policyUpdateResult.count} policies and terms`);

  console.log("Migration completed successfully");
}

main().catch((e) => {
  console.error("Migration failed:", e);
  process.exit(1);
});
