{"name": "@meeeetup/ui", "version": "0.0.0", "type": "module", "private": true, "exports": {".": "./index.ts", "./attendance-status": "./components/attendance-status/index.tsx", "./avatar": "./components/avatar/index.tsx", "./button": "./components/button/Button.tsx", "./event-registeration-card": "./components/event-registeration-card/index.tsx", "./meeeetup-logo": "./components/meeeetup-logo/index.tsx", "./otp": "./components/otp/Otp.tsx", "./status-indicator": "./components/status-indicator/index.tsx", "./utils": "./utils/index.ts", "./drawer": "./components/drawer/drawer.tsx", "./upcoming-event-card": "./components/upcoming-event-card/index.tsx", "./calendar": "./components/calendar/calendar.tsx", "./icon": "./components/icon/index.ts", "./input": "./components/input/input.tsx", "./password-input": "./components/password-input/password-input.tsx", "./radio-group": "./components/radio-group/index.ts", "./checkbox": "./components/checkbox/index.ts", "./switch": "./components/switch/index.ts", "./logout-dialog": "./components/logout-dialog/logout-dialog.tsx", "./favourite-button": "./components/favourite-button/index.ts", "./toaster": "./components/toast/index.ts", "./auth-page-layout": "./components/auth-page-layout/index.ts", "./privacy-policy-dialog": "./components/privacy-policy-dialog/index.ts", "./terms-of-use-dialog": "./components/terms-of-use-dialog/index.ts", "./popover": "./components/popover/index.ts", "./table": "./components/table/index.tsx", "./dash-side-bar": "./components/dash-side-bar/index.ts", "./event-list": "./components/event-list/index.ts", "./dialog": "./components/dialog/index.ts", "./event-detail-banner": "./components/event-detail-banner/index.ts", "./card": "./components/card/index.ts", "./participants-list": "./components/participants-list/index.ts", "./alert-dialog": "./components/alert-dialog/index.ts", "./textarea": "./components/textarea/index.ts", "./form-questions": "./components/form-questions/index.ts", "./profile-card": "./components/profile-card/index.ts", "./info-card": "./components/info-card/index.ts", "./search-bar": "./components/search-bar/index.ts", "./event-by-you": "./components/profile-card/event-by-you-dialog/index.ts", "./tabs": "./components/tabs/index.ts", "./question-card": "./components/question-card/index.ts", "./select": "./components/select/index.ts", "./facial-reception-capture": "./components/facial-reception-capture/index.ts", "./face-confirmation": "./components/face-confirmation/index.ts", "./accordian": "./components/accordian/index.ts", "./event-info-details": "./components/event-info-details/index.ts", "./quantity-selector": "./components/quantity-selector/index.ts", "./qrcode": "./components/qrcode/index.ts", "./skeleton": "./components/skeleton/index.ts", "./social-link-editor": "./components/social-link-editor/index.ts", "./form/participant": "./form/participant/index.ts", "./map": "./components/map/index.ts", "./event-preview": "./components/event-preview/index.ts", "./rich-editor": "./components/rich-editor/index.ts", "./participant-info-display": "./components/participant-info-display/index.tsx", "./dropdown-menu": "./components/dropdown-menu/index.tsx", "./featured-events-section": "./components/featured-events-section/index.tsx", "./feature-event-card": "./components/feature-event-card/index.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "check-types": "tsc --noEmit"}, "devDependencies": {"@meeeetup/config": "*", "@meeeetup/eslint-config": "*", "@meeeetup/typescript-config": "*", "@storybook/blocks": "8.5.8", "@storybook/react": "8.5.8", "@turbo/gen": "^1.12.4", "@types/leaflet": "^1", "@types/node": "^20.11.24", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "typescript": "5.5.4"}, "dependencies": {"@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.4", "@react-google-maps/api": "^2.20.6", "@tanstack/react-table": "^8.20.6", "@tiptap/extension-bullet-list": "^2.11.7", "@tiptap/extension-code-block": "^2.11.7", "@tiptap/extension-heading": "^2.11.7", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-ordered-list": "^2.11.7", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "input-otp": "^1.4.2", "lucide-react": "^0.474.0", "motion": "^12.4.2", "next": "^15.1.4", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "vaul": "^1.1.2"}}