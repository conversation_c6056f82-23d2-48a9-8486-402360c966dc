"use client";
import { LoaderIcon, XCircleIcon } from "lucide-react";
import { Button } from "@meeeetup/ui";
import { Textarea } from "@meeeetup/ui/textarea";
import { useState } from "react";

interface AddNoteFormProps {
  isAddingNote: boolean;
  addNoteError: string | null;
  onAddNote: (noteText: string) => Promise<void>;
  showAddNoteSection: boolean;
  setShowAddNoteSection: (show: boolean) => void;
}

export const AddNoteForm = ({
  isAddingNote,
  addNoteError,
  onAddNote,
  showAddNoteSection,
  setShowAddNoteSection,
}: AddNoteFormProps) => {
  const [newNoteText, setNewNoteText] = useState("");

  const handleAddNoteClick = async () => {
    await onAddNote(newNoteText);
    if (!addNoteError) {
      // only reset if there was no error during add
      setNewNoteText("");
    }
  };

  return (
    <div className="">
      <div className="flex items-center justify-between w-full">
        <Button
          variant="secondary"
          aria-label={showAddNoteSection ? "Collapse add note section" : "Expand add note section"}
          onClick={() => setShowAddNoteSection(!showAddNoteSection)}
          aria-expanded={showAddNoteSection}
          aria-controls="add-note-section-content"
          className="bg-blue-100 hover:bg-blue-200 text-blue-600 hover:text-blue-800 font-bold py-2 px-4 rounded border-0">
          {showAddNoteSection && <XCircleIcon className="size-5 mr-2" />}
          Add New Note
        </Button>
      </div>
      {showAddNoteSection && (
        <div id="add-note-section-content" className="mt-4">
          <Textarea
            placeholder="Enter note text here..."
            value={newNoteText}
            onChange={(e) => setNewNoteText(e.target.value)}
            disabled={isAddingNote}
            rows={3}
          />
          {addNoteError && <p className="text-red-500 text-sm mt-2">{addNoteError}</p>}
          <Button
            variant={"primary"}
            type="button"
            onClick={handleAddNoteClick}
            disabled={isAddingNote || !newNoteText.trim()}
            className="mt-3 w-full">
            {isAddingNote ? (
              <>
                <LoaderIcon className="size-3 mr-2 animate-spin" />
                Adding Note...
              </>
            ) : (
              "Add Note"
            )}
          </Button>
        </div>
      )}
    </div>
  );
};
