"use client";
import { LoaderIcon } from "lucide-react";
import { But<PERSON> } from "@meeeetup/ui";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from "@meeeetup/ui/dialog";
import { useEventParticipantNotes } from "./hooks";
import { useState } from "react";
import { GetNoteApiResponseData } from "../../_api";
import { NoteCard } from "./note-card";
import { AddNoteForm } from "./add-note-form";

interface NotesDialogProps {
  eventId: string;
  participantId: string;
}

export const NotesDialog = ({ eventId, participantId }: NotesDialogProps) => {
  const { useGetNotes, useAddNote, useUpdateNote } = useEventParticipantNotes();

  const {
    data: notesData,
    isLoading: isLoadingNotes,
    error: notesError,
  } = useGetNotes(eventId, participantId);
  const notes = notesData || [];

  const addNoteMutation = useAddNote();
  const updateNoteMutation = useUpdateNote();

  const [addNoteError, setAddNoteError] = useState<string | null>(null);
  const [updateNoteError, setUpdateNoteError] = useState<string | null>(null);

  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [showAddNoteSection, setShowAddNoteSection] = useState(false);

  const handleAddNote = async (noteText: string) => {
    setAddNoteError(null);
    if (!noteText.trim()) {
      setAddNoteError("Note cannot be empty.");
      return;
    }
    try {
      await addNoteMutation.mutateAsync({
        eventId,
        participantId,
        noteText,
      });
      // Resetting form and error is handled in AddNoteForm or by query invalidation effects
      if (!addNoteMutation.isError) {
        setShowAddNoteSection(false); // Collapse form on success
      }
    } catch (error: any) {
      setAddNoteError(error.message || "Failed to add note.");
    }
  };

  const handleEditNote = (note: GetNoteApiResponseData) => {
    setShowAddNoteSection(false);
    setEditingNoteId(note.id);
    setUpdateNoteError(null); // Clear previous update errors when starting a new edit
  };

  const handleSaveNote = async (noteId: string, newText: string) => {
    setUpdateNoteError(null);
    if (!newText.trim()) {
      setUpdateNoteError("Note text cannot be empty.");
      return;
    }
    try {
      await updateNoteMutation.mutateAsync({
        eventId,
        participantId,
        noteId: noteId,
        noteText: newText,
      });
      if (!updateNoteMutation.isError) {
        setEditingNoteId(null);
      }
    } catch (error: any) {
      setUpdateNoteError(error.message || "Failed to update note.");
    }
  };

  const handleCancelEdit = () => {
    setEditingNoteId(null);
    setUpdateNoteError(null);
  };

  const activeNotesCount = notes.length;

  return (
    <Dialog
      onOpenChange={(open) => {
        if (!open) {
          // Reset states when dialog is closed
          setEditingNoteId(null);
          setShowAddNoteSection(false);
          setAddNoteError(null);
          setUpdateNoteError(null);
        }
      }}>
      <DialogTrigger asChild>
        <Button
          variant={"secondary"}
          className="flex border-0 items-center gap-2 text-sm text-blue-600 hover:text-blue-700 bg-blue-100 hover:bg-blue-200">
          {isLoadingNotes ? (
            <LoaderIcon className="size-4 animate-spin" />
          ) : (
            <>
              <span>Notes</span>
              {activeNotesCount > 0 && (
                <span className="ml-2 inline-flex items-center justify-center px-2 py-0.5 text-xs font-bold leading-none text-white bg-gray-500 rounded-full">
                  {activeNotesCount}
                </span>
              )}
            </>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[625px] w-[calc(100%-2rem)] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Participant Notes</DialogTitle>
          <DialogDescription>View and add notes for this participant.</DialogDescription>
        </DialogHeader>

        <AddNoteForm
          isAddingNote={addNoteMutation.isPending}
          addNoteError={addNoteError}
          onAddNote={handleAddNote}
          showAddNoteSection={showAddNoteSection}
          setShowAddNoteSection={setShowAddNoteSection}
        />

        <div className="pb-4 flex-1 overflow-y-auto mt-4 pr-1">
          <h4 className="text-base font-semibold mb-2 sticky top-0 bg-white py-2 z-10">Existing Notes</h4>

          {isLoadingNotes ? (
            <div className="flex justify-center items-center h-20">
              <LoaderIcon className="size-6 animate-spin text-blue-500" />
            </div>
          ) : notesError ? (
            <p className="text-center text-red-500 p-4 bg-red-50 rounded-lg">
              Failed to load notes: {notesError.message}
            </p>
          ) : notes.length > 0 ? (
            <div className="space-y-4">
              {notes.map((note) => (
                <NoteCard
                  key={note.id}
                  note={note}
                  isSavingNote={updateNoteMutation.isPending && editingNoteId === note.id}
                  updateNoteError={updateNoteError}
                  onSave={handleSaveNote}
                  onEdit={handleEditNote}
                  onCancelEdit={handleCancelEdit}
                  isEditingThisNote={editingNoteId === note.id}
                />
              ))}
            </div>
          ) : (
            <p className="text-center text-gray-500 p-8 bg-gray-50 rounded-lg">
              No notes found for this participant.
            </p>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
