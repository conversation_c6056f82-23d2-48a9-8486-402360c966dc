import { prisma } from "@meeeetup/prisma";

import { createId } from "@/lib/api/utilts";

async function createFaceAuth({
  participantId,
  s3Url,
  externalImageId,
  faceId,
  imageId,
}: {
  participantId: string;
  s3Url: string;
  externalImageId: string;
  faceId: string;
  imageId: string;
}) {
  return await prisma.faceAuthData.create({
    data: {
      id: createId({ prefix: "fa_", length: 18 }),
      s3Url,
      externalImageId,
      faceId,
      imageId,
      faceRegisteredAt: new Date(),
      participantId,
    },
  });
}

async function findFaceAuth({
  // externalImageId,
  // faceId,
  imageId,
}: {
  // externalImageId: string;
  // faceId: string;
  imageId: string;
}) {
  return await prisma.faceAuthData.findFirst({
    where: {
      // externalImageId,
      // faceId,
      imageId,
    },
    include: {
      participant: true,
    },
  });
}

export { createFaceAuth, findFaceAuth };
