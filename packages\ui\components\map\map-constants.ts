import { Libraries } from "@react-google-maps/api";

// Define libraries as a static array
export const GOOGLE_MAPS_LIBRARIES: Libraries = ["places"];

// Default map container style
export const DEFAULT_CONTAINER_STYLE = {
  width: "100%",
  height: "200px", // Default height, can be overridden by props
  borderRadius: "0.375rem",
};

// Default center (e.g., Tokyo)
export const DEFAULT_MAP_CENTER = {
  lat: 35.6812,
  lng: 139.7671,
};

// Default zoom level
export const DEFAULT_ZOOM_LEVEL = 14;
export const DEFAULT_STATIC_MAP_ZOOM_LEVEL = 11;
export const SELECTED_LOCATION_ZOOM_LEVEL = 15;
