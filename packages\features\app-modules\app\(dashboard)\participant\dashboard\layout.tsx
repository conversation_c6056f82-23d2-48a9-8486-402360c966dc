import { redirect } from "next/navigation";

import { prisma } from "@meeeetup/prisma";

import { getServerAuthSession } from "@/auth/auth";

import FaceRegistrationDialog from "./_components/face-registration-dialog";

async function ParticipantDashboardLayout({ children }: { children: React.ReactNode }) {
  const session = await getServerAuthSession();
  const user = session?.user;

  if (!session || !user) {
    redirect("/login");
  }

  // Check if user has the participant role
  const userRole = user.activeRole?.name;
  if (userRole !== "participant") {
    redirect("/login");
  }

  // Continue with authenticated user
  const isFaceRegistered = await prisma.user
    .findUnique({
      where: {
        id: user.id,
      },
      include: { participantProfile: { select: { FaceAuthData: true } } },
    })
    .then((data) => data?.participantProfile?.FaceAuthData);

  return (
    <div>
      {!isFaceRegistered?.length && <FaceRegistrationDialog />}
      {children}
    </div>
  );
}

export default ParticipantDashboardLayout;
