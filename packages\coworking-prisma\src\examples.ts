/**
 * Examples of how to use the tenant-aware Prisma middleware
 */

import {
  prisma,
  withAppContext,
  getCurrentAppId,
  createTenantClient,
  createTenantMiddleware,
  TenantPrismaClient,
} from "./index";

// Example 1: Using withAppContext for manual tenant scoping
export async function example1_ManualTenantScoping() {
  const appId = "app-123";

  // All queries within this context will be automatically filtered by appId
  return withAppContext(appId, async () => {
    // This will only return events for app-123
    const events = await prisma.event.findMany({
      include: {
        host: true,
        ticketTypes: true,
        eventParticipant: true,
      },
    });

    // This will create an event with appId automatically set to 'app-123'
    const newEvent = await prisma.event.create({
      data: {
        hostId: "host-123",
        name: "Test Event",
        description: "A test event",
        startDatetime: new Date(),
        endDatetime: new Date(Date.now() + 3600000), // 1 hour later
        location: { address: "123 Main St" },
        tags: ["tech", "networking"],
      },
    });

    return { events, newEvent };
  });
}

// Example 2: Using TenantPrismaClient for cleaner API
export async function example2_TenantClient() {
  const tenantClient = createTenantClient("app-456");

  // All operations are automatically scoped to app-456
  const events = await tenantClient.getEvents({
    where: {
      status: "PUBLISHED",
    },
    include: {
      host: true,
      ticketTypes: true,
    },
  });

  const newEvent = await tenantClient.createEvent({
    hostId: "host-456",
    name: "Another Event",
    description: "Another test event",
    startDatetime: new Date(),
    endDatetime: new Date(Date.now() + 7200000), // 2 hours later
    location: { address: "456 Oak Ave" },
    tags: ["business", "conference"],
  });

  // You can also execute custom operations
  const customQuery = await tenantClient.execute(async () => {
    return prisma.event.findMany({
      where: {
        status: "PUBLISHED",
        startDatetime: {
          gte: new Date(),
        },
      },
      include: {
        eventParticipant: {
          include: {
            user: true,
          },
        },
      },
    });
  });

  return { events, newEvent, customQuery };
}

// Example 3: Express/Next.js middleware usage
export function example3_ExpressMiddleware() {
  const tenantMiddleware = createTenantMiddleware();

  // In Express
  /*
  app.use(tenantMiddleware);
  
  app.get('/events', async (req, res) => {
    // The tenant context is automatically set based on headers/subdomain/path
    const events = await prisma.event.findMany();
    res.json(events);
  });
  */

  // In Next.js API route
  /*
  export default async function handler(req, res) {
    return new Promise((resolve) => {
      tenantMiddleware(req, res, async () => {
        const events = await prisma.event.findMany();
        res.json(events);
        resolve();
      });
    });
  }
  */
}

// Example 4: Checking current tenant context
export async function example4_ContextChecking() {
  return withAppContext("app-789", async () => {
    const currentAppId = getCurrentAppId();
    console.log("Current app ID:", currentAppId); // Will log: app-789

    // Get events for current tenant
    const events = await prisma.event.findMany();

    // Get related data that will also be filtered
    const participants = await prisma.eventParticipant.findMany({
      include: {
        user: true,
        event: true,
      },
    });

    return { currentAppId, events, participants };
  });
}

// Example 5: Working with related models
export async function example5_RelatedModels() {
  const tenantClient = createTenantClient("app-999");

  return tenantClient.execute(async () => {
    // All these queries will be automatically filtered to only include
    // records related to events in app-999

    const eventParticipants = await prisma.eventParticipant.findMany({
      include: {
        user: true,
        event: true,
        ticket: true,
      },
    });

    const tickets = await prisma.ticket.findMany({
      include: {
        ticketType: true,
        event: true,
      },
    });

    const forms = await prisma.form.findMany({
      include: {
        questions: true,
        event: true,
      },
    });

    return { eventParticipants, tickets, forms };
  });
}

// Example 6: Error handling and edge cases
export async function example6_ErrorHandling() {
  try {
    // This will work fine
    const resultWithContext = await withAppContext("valid-app-id", async () => {
      return prisma.event.findMany();
    });

    // This will return all events (no filtering) because no context is set
    const resultWithoutContext = await prisma.event.findMany();

    // This will return null if the event doesn't belong to the current app
    const specificEvent = await withAppContext("app-123", async () => {
      return prisma.event.findUnique({
        where: { id: "event-from-different-app" },
      });
    });

    return { resultWithContext, resultWithoutContext, specificEvent };
  } catch (error) {
    console.error("Error in tenant operations:", error);
    throw error;
  }
}

// Example 7: Advanced filtering scenarios
export async function example7_AdvancedFiltering() {
  return withAppContext("advanced-app", async () => {
    // Complex where clauses are automatically enhanced with appId filter
    const events = await prisma.event.findMany({
      where: {
        OR: [{ status: "PUBLISHED" }, { status: "LIMITED_PUBLISHED" }],
        AND: [{ startDatetime: { gte: new Date() } }, { tags: { has: "tech" } }],
      },
    });

    // Nested filtering also works
    const hostsWithEvents = await prisma.hostProfile.findMany({
      include: {
        events: {
          where: {
            status: "PUBLISHED",
          },
        },
      },
    });

    return { events, hostsWithEvents };
  });
}
