"use client";

import { createContext, useContext, ReactNode } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@meeeetup/ui/toaster";
import { useFacialReceptionManager, QRCodeDialogData } from "../hooks/useFacialReceptionManager";

interface FacialReceptionContextType {
  state: ReturnType<typeof useFacialReceptionManager>;
}

const FacialReceptionContext = createContext<FacialReceptionContextType | null>(null);

interface FacialReceptionProviderProps {
  children: ReactNode;
  eventId: string;
}

export function FacialReceptionProvider({ children, eventId }: FacialReceptionProviderProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const state = useFacialReceptionManager({
    eventId,
    queryClient,
    toast,
  });

  return <FacialReceptionContext.Provider value={{ state }}>{children}</FacialReceptionContext.Provider>;
}

export function useFacialReceptionContext() {
  const context = useContext(FacialReceptionContext);
  if (!context) {
    throw new Error("useFacialReceptionContext must be used within a FacialReceptionProvider");
  }
  return context.state;
}
