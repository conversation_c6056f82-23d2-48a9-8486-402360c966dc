"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  GetNoteApiResponseData,
  PutNotesApiResponse,
  PostNotesApiResponse,
  PostNotesApiResponseData,
  PutNotesApiResponseData,
} from "../../_api";
import { useToast } from "@meeeetup/ui";
import { addNoteApi, getNotesApi, updateNote<PERSON>pi } from "../../_api";

const getNotes = async (eventId: string, participantId: string): Promise<GetNoteApiResponseData[]> => {
  const response = await getNotesApi(eventId, participantId);
  if (!response.success) {
    throw new Error(response.error.details || response.error.message || "Failed to fetch notes");
  }
  return response.data;
};

const addNote = async (data: {
  eventId: string;
  participantId: string;
  noteText: string;
}): Promise<PostNotesApiResponseData> => {
  const response = await addNote<PERSON><PERSON>({
    eventId: data.eventId,
    participantId: data.participantId,
    noteText: data.noteText,
  });
  if (!response.success) {
    throw new Error(response.error.details || response.error.message || "Failed to add note");
  }
  return response.data;
};

const updateNote = async (data: {
  eventId: string;
  participantId: string;
  noteId: string;
  noteText: string;
}): Promise<PutNotesApiResponseData> => {
  const response = await updateNoteApi({
    eventId: data.eventId,
    participantId: data.participantId,
    noteId: data.noteId,
    noteText: data.noteText,
  });
  if (!response.success) {
    throw new Error(response.error.details || response.error.message || "Failed to update note");
  }
  return response.data;
};

export const useEventParticipantNotes = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const useGetNotes = (eventId: string, participantId: string) => {
    return useQuery<GetNoteApiResponseData[], Error>({
      queryKey: ["notes", eventId, participantId],
      queryFn: () => getNotes(eventId, participantId),
    });
  };

  const useAddNote = () => {
    return useMutation<
      PostNotesApiResponseData,
      Error,
      { eventId: string; participantId: string; noteText: string }
    >({
      mutationFn: addNote,
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({
          queryKey: ["notes", variables.eventId, variables.participantId],
        });
        toast({ title: "Success", description: "Note added successfully" });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to add note",
          variant: "destructive",
        });
      },
    });
  };

  const useUpdateNote = () => {
    return useMutation<
      PutNotesApiResponseData,
      Error,
      {
        eventId: string;
        participantId: string;
        noteId: string;
        noteText: string;
      }
    >({
      mutationFn: updateNote,
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({
          queryKey: ["notes", variables.eventId, variables.participantId],
        });
        toast({ title: "Success", description: "Note updated successfully" });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to update note",
          variant: "destructive",
        });
      },
    });
  };

  return { useGetNotes, useAddNote, useUpdateNote };
};
