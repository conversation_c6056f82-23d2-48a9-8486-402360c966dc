import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@meeeetup/prisma";
import { withHostRole } from "@/middleware-helpers/with-host-role";
import { Parser } from "json2csv";
import { errors } from "@/lib/errors";
import { responseJson } from "@/lib/response";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  await withHostRole();
  const { id: eventId } = await params;

  const eventParticipants = await prisma.eventParticipant.findMany({
    where: {
      eventId,
      status: {
        not: {
          in: ["CANCELLED"],
        },
      },
    },
    include: {
      user: {
        include: {
          participantProfile: true,
        },
      },
      ticket: {
        include: {
          ticketType: true,
        },
      },
      formSubmission: {
        include: {
          answers: {
            include: {
              question: true,
            },
          },
        },
      },
      checkInOutRecord: true,
      notes: true,
      event: true,
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  if (!eventParticipants.length) {
    throw errors.notFound("Event participants to export not found!");
  }

  // Collect all unique question texts across all participants
  const allQuestionTexts = new Set<string>();
  eventParticipants.forEach((participant) => {
    participant.formSubmission.forEach((submission) => {
      submission.answers.forEach((answer) => {
        if (answer.question?.questionText) {
          allQuestionTexts.add(answer.question.questionText);
        }
      });
    });
  });

  // Transform participants data
  const participants = eventParticipants.map((participant) => {
    const customAnswers: { [key: string]: string } = {};

    // Initialize all questions with empty values
    allQuestionTexts.forEach((questionText) => {
      customAnswers[questionText] = "";
    });

    // Fill in actual answers
    participant.formSubmission.forEach((submission) => {
      submission.answers.forEach((answer) => {
        if (answer.question?.questionText) {
          // Handle JSON answers: if it's an object, stringify it; otherwise, use its value
          customAnswers[answer.question.questionText] =
            typeof answer.answer === "object" && answer.answer !== null
              ? JSON.stringify(answer.answer)
              : String(answer.answer || "");
        }
      });
    });

    return {
      id: participant.id,
      name: participant.user?.participantProfile?.name || participant.guestName || "",
      email: participant.user?.participantProfile?.email || participant.guestEmail || "",
      appliedDate: participant.updatedAt,
      status: participant.status ?? "PENDING",
      eventName: participant.event.name,
      ticketTypeName: participant.ticket?.ticketType.name || "",
      ...customAnswers,
    };
  });

  // Create dynamic fields array including all question texts
  const baseFields = ["id", "name", "email", "appliedDate", "status", "eventName", "ticketTypeName"];

  const dynamicFields = Array.from(allQuestionTexts);
  const allFields = [...baseFields, ...dynamicFields];

  const opts = { fields: allFields };

  try {
    const parser = new Parser(opts);
    const csv = parser.parse(participants);

    return NextResponse.json(
      {
        success: true,
        data: csv,
        message: "Export to csv successfully!",
      },
      {
        status: 200,
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename=Users.csv`,
        },
      }
    );
  } catch (err) {
    return responseJson.error({ error: err, message: "Fail to export as csv!" });
  }
}
