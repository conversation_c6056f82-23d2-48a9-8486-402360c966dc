"use client";

import { useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { QueryClient } from "@tanstack/react-query";
import { useToast } from "@meeeetup/ui/toaster";
import { z } from "zod";

import { useFaceDetection } from "./useFaceDetection";
import { useConfirmation } from "./useConfirmation";
import { useFacialReceptionState } from "./useFacialReceptionState";
import { PotentialMatchUser } from "../types";
import { searchByEmailOrNameResponseSchema } from "@/schema/host/event-participant/searchByEmailOrName";
import { registerFaceForUserApi } from "../_api";

export interface QRCodeDialogData {
  url: string;
  userName?: string;
}

interface UseFacialReceptionManagerProps {
  eventId: string;
  queryClient: QueryClient;
  toast: ReturnType<typeof useToast>["toast"];
}

export function useFacialReceptionManager({ eventId, queryClient, toast }: UseFacialReceptionManagerProps) {
  const router = useRouter();
  const { state, dispatch, currentUser } = useFacialReceptionState();

  const detectMutation = useFaceDetection({ eventId, toast, dispatch });
  const confirmMutation = useConfirmation({ eventId, queryClient, toast, dispatch });

  const { status, capturedImage, potentialMatches } = state;

  const baseUrl = typeof window !== "undefined" ? `${window.location.origin}` : "";
  const participantEventUrl = `${baseUrl}/participant/dashboard/events/${eventId}`;

  const handleCapture = useCallback(
    (imageBase64: string) => {
      detectMutation.mutate(imageBase64);
    },
    [detectMutation]
  );

  const handleConfirmCurrentMatch = useCallback(() => {
    if (!currentUser) return;

    const performConfirm = (userToConfirm: PotentialMatchUser) => {
      confirmMutation.mutate(userToConfirm, {
        onSuccess: (data) => {
          if (data?.success && data.data?.autoLoginUrl) {
            dispatch({
              type: "SHOW_QR_CODE",
              payload: {
                url: data.data.autoLoginUrl,
                userName: userToConfirm.name,
              },
            });
          }
        },
      });
    };

    if (capturedImage && status === "FACE_NOT_FOUND" && !currentUser.isFaceRegistered) {
      dispatch({ type: "START_REGISTRATION" });
      registerFaceForUserApi(capturedImage, currentUser.userId, eventId)
        .then((response) => {
          if (response.success) {
            toast({
              title: "Face Registered",
              description: `The face has been associated with ${
                currentUser.name || currentUser.email
              }. Proceeding to check-in.`,
            });
            performConfirm({ ...currentUser, isFaceRegistered: true });
          } else {
            toast({
              title: "Registration Failed",
              description: response.error?.message || "Could not register the face.",
              variant: "destructive",
            });
            performConfirm(currentUser);
          }
        })
        .catch((error) => {
          console.error("Error registering face:", error);
          toast({
            title: "Registration Error",
            description: "An unexpected error occurred while registering the face.",
            variant: "destructive",
          });
          performConfirm(currentUser);
        });
    } else {
      performConfirm(currentUser);
    }
  }, [currentUser, capturedImage, status, eventId, confirmMutation, toast, dispatch]);

  useEffect(() => {
    if (status === "MATCHES_FOUND" && !state.isManualSelection && potentialMatches?.length === 1) {
      handleConfirmCurrentMatch();
    }
  }, [status, potentialMatches, state.isManualSelection, handleConfirmCurrentMatch]);

  const handleCancelAll = useCallback(() => {
    dispatch({ type: "RESET" });
    toast({ title: "Cancelled", description: "Face check-in was cancelled" });
  }, [toast, dispatch]);

  const handleSelectParticipant = useCallback(
    (participant: z.infer<typeof searchByEmailOrNameResponseSchema>["participants"][0]) => {
      const matchUser: PotentialMatchUser = {
        userId: participant.id,
        name: participant.name || participant.email,
        email: participant.email,
        image: participant.image || "",
        isFaceRegistered: participant.isFaceRegistered,
      };
      dispatch({ type: "MANUAL_MATCH_SELECTED", payload: matchUser });
    },
    [dispatch]
  );

  const handleCloseQRCodeDialogAndContinue = useCallback(() => {
    dispatch({ type: "CLOSE_QR_CODE" });
  }, [dispatch]);

  const handleNavigateBack = useCallback(() => {
    router.back();
  }, [router]);

  return {
    state,
    dispatch,
    currentUser,
    detectMutation,
    confirmMutation,
    participantEventUrl,
    handleCapture,
    handleConfirmCurrentMatch,
    handleCancelAll,
    handleSelectParticipant,
    handleCloseQRCodeDialogAndContinue,
    handleNavigateBack,
  };
}
