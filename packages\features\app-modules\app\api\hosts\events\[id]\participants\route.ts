import { NextRequest } from "next/server";

import { CheckInOutActionType, prisma, Prisma } from "@meeeetup/prisma";

import { createId } from "@/lib/api/utilts";
import { errors } from "@/lib/errors";
import { responseJson } from "@/lib/response";
import { zodParse } from "@/lib/zod";
import { withHostRole } from "@/middleware-helpers/with-host-role";
import { eventParticipantFilterSchema } from "@/schema/filters/participant-filter";
import manualRegisterSchema from "@/schema/participant/manual-register";
import { getEventParticipantsByEventId } from "@/services/event-participants.service";

// /api/hosts/events/[id]/participants?page=1&pageSize=10&order=createdAt:desc
async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  let session;
  try {
    session = await withHostRole();
    const { id: eventId } = await params;

    const searchParams = request.nextUrl.searchParams;

    const page = searchParams.get("page") || "1";
    const pageSize = searchParams.get("pageSize") || "10";
    const order = searchParams.get("order") || "createdAt:desc";
    const searchQuery = searchParams.get("searchQuery") || "";

    const filterOptions = await zodParse(eventParticipantFilterSchema, {
      page: Number(page),
      pageSize: Number(pageSize),
      order,
      searchQuery,
    });

    const eventParticipants = await getEventParticipantsByEventId(eventId, filterOptions);
    if (!eventParticipants) throw errors.notFound("No events found!");

    return responseJson.success({
      data: eventParticipants,
      message: "Events retrieved successfully!",
      logContext: { session },
    });
  } catch (error) {
    return responseJson.error({ error, message: "Error getting events!", logContext: { session } });
  }
}

// /api/hosts/events/[id]/participants
/* body
{
    guestEmail: string;
    guestName: string;
    ticketTypeId: string;
}
*/
async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const session = await withHostRole();

  const { id: eventId } = await params;
  if (!eventId) throw errors.badRequest("No id provided!");

  const body = await request.json();
  const parsedBody = await zodParse(manualRegisterSchema, { ...body, eventId });

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate ticket availability first
      const ticketType = await tx.ticketType.findUnique({
        where: { id: parsedBody.ticketTypeId },
      });

      if (!ticketType) throw errors.notFound("Ticket type not found!");

      const ticketsSold = await tx.ticket.count({
        where: { ticketTypeId: parsedBody.ticketTypeId },
      });

      if (ticketsSold >= ticketType.quantity) {
        throw errors.badRequest("All tickets are sold out!");
      }

      // Check if user with email already exists
      let user = await tx.user.findUnique({
        where: { email: parsedBody.guestEmail },
      });

      // If user doesn't exist, create user and participant profile
      if (!user) {
        // Create new user
        user = await tx.user.create({
          data: {
            id: createId({ prefix: "usr_", length: 22 }),
            email: parsedBody.guestEmail,
            name: parsedBody.guestName || undefined,
            // Create participant profile for the user
            participantProfile: {
              create: {
                email: parsedBody.guestEmail,
                name: parsedBody.guestName || undefined,
              },
            },
            // Assign participant role to the user
            roles: {
              connect: {
                name: "participant",
              },
            },
          },
        });
      }

      // Create payment
      const payment = await tx.payment.create({
        data: {
          id: createId({ prefix: "pm_", length: 10 }),
          amount: ticketType.price,
          currency: "usd",
          status: "COMPLETED",
          paymentMethod: "MANUAL",
          transactionId: createId({ prefix: "manual_", length: 12 }),
        },
      });

      // Create ticket with the actual user ID
      const ticket = await tx.ticket.create({
        data: {
          id: createId({ prefix: "tk_", length: 12 }),
          eventId,
          ticketTypeId: parsedBody.ticketTypeId,
          paymentId: payment.id,
          status: "PAID",
          userId: user.id, // Use the real user ID instead of "GUEST"
          checkedInAt: new Date(),
        },
      });

      let eventParticipant = null;
      try {
        eventParticipant = await tx.eventParticipant.create({
          data: {
            id: createId({ prefix: "ept_", length: 22 }),
            eventId,
            userId: user.id, // Connect to the user
            checkInMethod: "MANUAL",
            checkInTime: new Date(),
            status: "CHECKED_IN",
            ticketId: ticket.id,
          },
          include: {
            ticket: true,
            event: true,
            user: {
              include: {
                participantProfile: true,
              },
            },
          },
        });
        // check in record to in/out table
        if (eventParticipant) {
          await tx.checkInOutRecord.create({
            data: {
              participantId: eventParticipant.id,
              actionType: CheckInOutActionType.CHECK_IN,
              actionTime: new Date(), // check in time
              method: "MANUAL",
            },
          });
        }
      } catch (error) {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2002") {
            throw errors.badRequest("User already registered for this event.");
          }
        }
      }

      return {
        eventParticipant,
        ticket,
        user,
      };
    });

    return responseJson.success({
      data: result,
      message: "Participant registered successfully!",
      logContext: { session },
    });
  } catch (error) {
    return responseJson.error({
      error,
      message: "An error occurred while creating the event participant.",
      logContext: { session },
    });
  }
}

export { GET, POST };
