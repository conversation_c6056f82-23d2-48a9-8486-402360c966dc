import { z } from "zod";

const validPublicEventOrderFields = [
  "createdAt",
  "updatedAt",
  "name",
  "startDatetime",
  "endDatetime",
] as const;

const orderDirections = ["asc", "desc"] as const;

export type ValidOrderField = (typeof validPublicEventOrderFields)[number];
export type OrderDirection = (typeof orderDirections)[number];
export type ValidOrder = `${ValidOrderField}:${OrderDirection}`;

// Create Zod schemas for the order components
const orderFieldSchema = z.enum(validPublicEventOrderFields);
const orderDirectionSchema = z.enum(orderDirections);

// Type-safe order schema that validates the format and components
const orderSchema = z
  .string()
  .refine(
    (val): val is ValidOrder => {
      if (!val) return false;
      const parts = val.split(":");
      if (parts.length !== 2) return false;

      const [field, direction] = parts;
      return orderFieldSchema.safeParse(field).success && orderDirectionSchema.safeParse(direction).success;
    },
    {
      message: `Order must be in format 'field:direction' where field is one of [${validPublicEventOrderFields.join(", ")}] and direction is one of [${orderDirections.join(", ")}]`,
    }
  )
  .transform((val): ValidOrder => val as ValidOrder);

// Helper function to validate order string
export function isValidOrder(order: string): order is ValidOrder {
  return orderSchema.safeParse(order).success;
}

// Type for raw filter input before validation
export interface RawPublicEventFilters {
  page?: number;
  pageSize?: number;
  searchQuery?: string;
  order?: ValidOrder | string; // Allow string for validation flexibility, but prefer ValidOrder
  tags?: string[];
  startDateFrom?: string;
  startDateTo?: string;
  hostId?: string;
}

export const publicEventFilterSchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  pageSize: z.coerce.number().min(1).max(100).optional().default(10),
  order: orderSchema.optional(),
  searchQuery: z.string().min(1).optional(),
  tags: z.array(z.string().min(1)).optional(),
  startDateFrom: z.string().datetime().optional(),
  startDateTo: z.string().datetime().optional(),
  hostId: z.string().uuid().optional(),
});

export type PublicEventFilterOptions = z.infer<typeof publicEventFilterSchema>;
