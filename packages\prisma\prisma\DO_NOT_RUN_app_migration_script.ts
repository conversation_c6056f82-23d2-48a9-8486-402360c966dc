// @ts-nocheck
import {
  PrismaClient,
  Prisma,
  EventStatus,
  RoleName,
  VerificationTokenType,
  QuestionType,
  ParticipantStatus,
  CheckInMethod,
  TicketStatus,
  PaymentStatus,
  CheckInOutActionType,
  NoteType,
} from "@prisma/client";
import { PrismaClient as OldPrismaClient } from "./generated/prisma/client";
import {
  PrismaClient as NewPrismaClient,
  type Prisma as NewPrisma,
} from "../../coworking-prisma/prisma/generated/prisma/client";

// Initialize Prisma Client for the new database (target)
// Ensure DATABASE_URL for the new DB is set in your .env file
const newPrisma = new NewPrismaClient({
  datasources: {
    db: {
      url: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require",
    },
  },
  // log: ['query', 'info', 'warn', 'error'], // Uncomment for detailed Prisma logs
  transactionOptions: { timeout: 1800000 }, // 30 minute timeout for transactions
});

// Initialize Prisma Client for the old database (source)
const OLD_DATABASE_URL =
  "postgresql://meeeetup_db_owner:<EMAIL>/meeeetup_db?sslmode=require";
const oldPrisma = new OldPrismaClient({
  datasources: {
    db: {
      url: OLD_DATABASE_URL,
    },
  },

  transactionOptions: { timeout: 1800000 }, // 30 minute timeout for transactions
});

const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  debug: (message: string, ...args: any[]) => {
    if (process.env.DEBUG_MIGRATION || true) {
      // Set to true for more verbose debug logging
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  },
};

const BATCH_SIZE = 50;

function getTimestamps(oldEntity: any) {
  return {
    ...(oldEntity.createdAt && { createdAt: new Date(oldEntity.createdAt) }),
    ...(oldEntity.updatedAt && { updatedAt: new Date(oldEntity.updatedAt) }),
  };
}

async function migrateRoles(tx: any, oldDb: OldPrismaClient): Promise<Map<string, string>> {
  logger.info("Migrating Roles...");
  const roleIdMap = new Map<string, string>();
  let DBMigrationCounter = 0;
  let processedCounter = 0;

  const oldRoles = await oldDb.role.findMany();
  processedCounter = oldRoles.length;

  for (const oldRole of oldRoles) {
    try {
      const existingRole = await tx.role.findUnique({
        where: { name: oldRole.name as RoleName },
      });

      if (existingRole) {
        roleIdMap.set(oldRole.id, existingRole.id);
        logger.debug(
          `Role '${oldRole.name}' already exists in new DB. Old ID: ${oldRole.id}, New ID: ${existingRole.id}`
        );
      } else {
        const newRole = await tx.role.create({
          data: {
            name: oldRole.name as RoleName,
            ...getTimestamps(oldRole),
          },
        });
        roleIdMap.set(oldRole.id, newRole.id);
        DBMigrationCounter++;
        logger.debug(`Migrated Role: ${newRole.name} (Old ID: ${oldRole.id}, New ID: ${newRole.id})`);
      }
    } catch (error: any) {
      logger.error(`Failed to migrate role (Old ID: ${oldRole.id}, Name: ${oldRole.name}): ${error.message}`);
    }
  }
  logger.info(
    `Roles migration: ${DBMigrationCounter} created, ${processedCounter - DBMigrationCounter} skipped (already exist). Total processed: ${processedCounter}.`
  );
  return roleIdMap;
}

async function migrateCompanies(tx: any, oldDb: OldPrismaClient): Promise<Map<string, string>> {
  logger.info("Migrating Companies...");
  const companyIdMap = new Map<string, string>();
  let DBMigrationCounter = 0;
  let processedCounter = 0;

  const oldCompanies = await oldDb.company.findMany();
  processedCounter = oldCompanies.length;

  for (const oldCompany of oldCompanies) {
    try {
      const existingCompany = await tx.company.findFirst({
        where: { name: oldCompany.name },
      });

      if (existingCompany) {
        companyIdMap.set(oldCompany.id, existingCompany.id);
        logger.debug(
          `Company '${oldCompany.name}' (Old ID: ${oldCompany.id}) may already exist as New ID: ${existingCompany.id}. Using existing.`
        );
      } else {
        const companyData: Prisma.CompanyCreateInput = {
          name: oldCompany.name,
          description: oldCompany.description,
          email: oldCompany.email,
          phoneNumber: oldCompany.phoneNumber,
          websiteUrl: oldCompany.websiteUrl,
          socialLinks: oldCompany.socialLinks || Prisma.JsonNull,
          verificationStatus: oldCompany.verificationStatus,
          ...getTimestamps(oldCompany),
        };
        const newCompany = await tx.company.create({ data: companyData });
        companyIdMap.set(oldCompany.id, newCompany.id);
        DBMigrationCounter++;
        logger.debug(
          `Migrated Company: ${newCompany.name} (Old ID: ${oldCompany.id}, New ID: ${newCompany.id})`
        );
      }
    } catch (error: any) {
      logger.error(
        `Failed to migrate company (Old ID: ${oldCompany.id}, Name: ${oldCompany.name}): ${error.message}`
      );
    }
  }
  logger.info(
    `Companies migration: ${DBMigrationCounter} created, ${processedCounter - DBMigrationCounter} potentially skipped. Total processed: ${processedCounter}.`
  );
  return companyIdMap;
}

async function migrateUsersAndAssociatedData(
  tx: any,
  oldDb: OldPrismaClient,
  roleIdMap: Map<string, string>,
  companyIdMap: Map<string, string>
): Promise<Map<string, string>> {
  logger.info("Migrating Users and their associated data (Accounts, Profiles, etc.)...");
  const userIdMap = new Map<string, string>();
  let usersMigratedCount = 0;
  let usersSkippedCount = 0;
  let associatedDataMigratedCount = 0;

  const oldUsers = await oldDb.user.findMany({
    include: {
      accounts: true,
      participantProfile: {
        include: {
          education: true,
          workExperience: true,
          FaceAuthData: true,
        },
      },
      hostProfile: {
        include: {
          education: true,
          payoutInformation: true,
        },
      },
      roles: true,
    },
  });

  logger.info(`Found ${oldUsers.length} users in old database to process.`);

  for (const oldUser of oldUsers) {
    let newUserId = "";

    try {
      const existingUser = await tx.user.findUnique({
        where: { email: oldUser.email },
      });

      if (existingUser) {
        newUserId = existingUser.id;
        userIdMap.set(oldUser.id, newUserId);
        usersSkippedCount++;
        logger.info(
          `User '${oldUser.email}' (Old ID: ${oldUser.id}) already exists in new DB (New ID: ${newUserId}). Migrating associated data only.`
        );
      } else {
        const userData: Prisma.UserCreateInput = {
          email: oldUser.email,
          name: oldUser.name,
          stripeCustomerId: oldUser.stripeCustomerId,
          password: oldUser.password,
          phoneNumber: oldUser.phoneNumber,
          phoneNumberVerified: oldUser.phoneNumberVerified,
          emailVerified: oldUser.emailVerified,
          image: oldUser.image,
          activeRoleId: oldUser.activeRoleId ? roleIdMap.get(oldUser.activeRoleId) : null,
          ...getTimestamps(oldUser),
        };
        const newUser = await tx.user.create({ data: userData });
        newUserId = newUser.id;
        userIdMap.set(oldUser.id, newUserId);
        usersMigratedCount++;
        logger.info(`Migrated User: ${newUser.email} (Old ID: ${oldUser.id}, New ID: ${newUserId})`);
      }

      // Migrate Accounts
      for (const oldAccount of oldUser.accounts) {
        try {
          const existingAccount = await tx.account.findUnique({
            where: {
              provider_providerAccountId: {
                provider: oldAccount.provider,
                providerAccountId: oldAccount.providerAccountId,
              },
            },
          });
          if (existingAccount && existingAccount.userId === newUserId) {
            logger.debug(
              `Account for user ${newUserId} (Provider: ${oldAccount.provider}) already exists. Skipping.`
            );
          } else if (existingAccount && existingAccount.userId !== newUserId) {
            logger.warn(
              `Account (Provider: ${oldAccount.provider}, ID: ${oldAccount.providerAccountId}) exists but is tied to a different user ${existingAccount.userId} in new DB. Skipping migration for user ${newUserId}.`
            );
          } else {
            await tx.account.create({
              data: {
                user: { connect: { id: newUserId } },
                type: oldAccount.type,
                provider: oldAccount.provider,
                providerAccountId: oldAccount.providerAccountId,
                refresh_token: oldAccount.refresh_token,
                access_token: oldAccount.access_token,
                expires_at: oldAccount.expires_at,
                token_type: oldAccount.token_type,
                scope: oldAccount.scope,
                id_token: oldAccount.id_token,
                session_state: oldAccount.session_state,
                ...getTimestamps(oldAccount), // Account model does not have timestamps by default
              },
            });
            associatedDataMigratedCount++;
            logger.debug(`Migrated Account for user ${newUserId} (Provider: ${oldAccount.provider})`);
          }
        } catch (accError: any) {
          logger.error(
            `Failed to migrate account for user ${newUserId} (Old User ID: ${oldUser.id}, Account Provider: ${oldAccount.provider}): ${accError.message}`
          );
        }
      }

      // Migrate ParticipantProfile
      if (oldUser.participantProfile) {
        const oldProfile = oldUser.participantProfile;
        try {
          const existingProfile = await tx.participantProfile.findUnique({ where: { userId: newUserId } });
          if (existingProfile) {
            logger.debug(`ParticipantProfile for user ${newUserId} already exists. Skipping creation.`);
          } else {
            const profileData: Prisma.ParticipantProfileCreateInput = {
              user: { connect: { id: newUserId } },
              imgUrl: oldProfile.imgUrl,
              name: oldProfile.name,
              email: oldProfile.email,
              phoneNumber: oldProfile.phoneNumber,
              dob: oldProfile.dob ? new Date(oldProfile.dob) : null,
              gender: oldProfile.gender,
              bio: oldProfile.bio,
              socialLinks: oldProfile.socialLinks || Prisma.JsonNull,
              ...getTimestamps(oldProfile),
            };
            await tx.participantProfile.create({ data: profileData });
            associatedDataMigratedCount++;
            logger.debug(`Migrated ParticipantProfile for user ${newUserId}`);
          }

          // Migrate Education for ParticipantProfile
          for (const oldEdu of oldProfile.education) {
            try {
              const newEducationData: Prisma.EducationCreateInput = {
                userId: newUserId,
                participantProfile: { connect: { userId: newUserId } },
                university: oldEdu.university,
                major: oldEdu.major,
                degree: oldEdu.degree,
                startDate: oldEdu.startDate ? new Date(oldEdu.startDate) : null,
                endDate: oldEdu.endDate ? new Date(oldEdu.endDate) : null,
                ...getTimestamps(oldEdu),
              };
              const existingEdu = await tx.education.findFirst({
                where: {
                  userId: newUserId,
                  participantProfileUserId: newUserId,
                  university: oldEdu.university,
                  major: oldEdu.major,
                  degree: oldEdu.degree,
                },
              });
              if (existingEdu) {
                logger.debug(
                  `Education for participant ${newUserId} (Uni: ${oldEdu.university}) already exists. Skipping.`
                );
              } else {
                await tx.education.create({ data: newEducationData });
                associatedDataMigratedCount++;
              }
            } catch (eduError: any) {
              logger.error(
                `Failed to migrate Education for ParticipantProfile ${newUserId} (Old Edu ID: ${oldEdu.id}): ${eduError.message}`
              );
            }
          }
          // Migrate WorkExperience for ParticipantProfile
          for (const oldWork of oldProfile.workExperience) {
            try {
              const newCompanyId = oldWork.companyId ? companyIdMap.get(oldWork.companyId) : undefined;
              if (oldWork.companyId && !newCompanyId) {
                logger.warn(
                  `Could not find new company ID for old company ID ${oldWork.companyId} for WorkExperience ${oldWork.id}. Skipping company link.`
                );
              }
              const workData: Prisma.WorkExperienceCreateInput = {
                userId: newUserId,
                participantProfile: { connect: { userId: newUserId } },
                position: oldWork.position,
                startDate: new Date(oldWork.startDate), // Ensure this is valid date
                endDate: oldWork.endDate ? new Date(oldWork.endDate) : null,
                ...(newCompanyId && { company: { connect: { id: newCompanyId } } }),
                ...getTimestamps(oldWork),
              };
              const existingWork = await tx.workExperience.findFirst({
                where: {
                  userId: newUserId,
                  participantProfileUserId: newUserId,
                  position: oldWork.position,
                  companyId: newCompanyId,
                },
              });
              if (existingWork) {
                logger.debug(
                  `WorkExperience for participant ${newUserId} (Position: ${oldWork.position}) already exists. Skipping.`
                );
              } else {
                await tx.workExperience.create({ data: workData });
                associatedDataMigratedCount++;
              }
            } catch (workError: any) {
              logger.error(
                `Failed to migrate WorkExperience for ParticipantProfile ${newUserId} (Old Work ID: ${oldWork.id}): ${workError.message}`
              );
            }
          }
          // Migrate FaceAuthData for ParticipantProfile
          if (oldProfile.FaceAuthData) {
            // Check if FaceAuthData is an array and has items
            for (const oldFace of oldProfile.FaceAuthData) {
              try {
                const faceData: Prisma.FaceAuthDataCreateInput = {
                  participant: { connect: { userId: newUserId } },
                  faceId: oldFace.faceId,
                  imageId: oldFace.imageId,
                  externalImageId: oldFace.externalImageId,
                  s3Url: oldFace.s3Url,
                  faceRegisteredAt: new Date(oldFace.faceRegisteredAt),
                  ...getTimestamps(oldFace),
                };
                const existingFace = await tx.faceAuthData.findFirst({
                  where: {
                    participantId: newUserId,
                    faceId: oldFace.faceId,
                  },
                });
                if (existingFace) {
                  logger.debug(
                    `FaceAuthData for participant ${newUserId} (Face ID: ${oldFace.faceId}) already exists. Skipping.`
                  );
                } else {
                  await tx.faceAuthData.create({ data: faceData });
                  associatedDataMigratedCount++;
                }
              } catch (faceError: any) {
                logger.error(
                  `Failed to migrate FaceAuthData for ParticipantProfile ${newUserId} (Old Face ID: ${oldFace.id}): ${faceError.message}`
                );
              }
            }
          }
        } catch (ppError: any) {
          logger.error(
            `Failed to migrate ParticipantProfile for user ${newUserId} (Old User ID: ${oldUser.id}): ${ppError.message}`
          );
        }
      }

      // Migrate HostProfile
      if (oldUser.hostProfile) {
        const oldProfile = oldUser.hostProfile;
        try {
          const existingProfile = await tx.hostProfile.findUnique({ where: { userId: newUserId } });
          if (existingProfile) {
            logger.debug(`HostProfile for user ${newUserId} already exists. Skipping creation.`);
          } else {
            const newCompanyId = oldProfile.companyId ? companyIdMap.get(oldProfile.companyId) : undefined;
            if (oldProfile.companyId && !newCompanyId) {
              logger.warn(
                `HostProfile ${oldProfile.userId} linked to old company ${oldProfile.companyId}, but new company ID not found. Skipping company link.`
              );
            }
            const profileData: Prisma.HostProfileCreateInput = {
              user: { connect: { id: newUserId } },
              imgUrl: oldProfile.imgUrl,
              bio: oldProfile.bio,
              gender: oldProfile.gender,
              dob: oldProfile.dob ? new Date(oldProfile.dob) : null,
              socialLinks: oldProfile.socialLinks || Prisma.JsonNull,
              name: oldProfile.name,
              email: oldProfile.email,
              phoneNumber: oldProfile.phoneNumber,
              rolePosition: oldProfile.rolePosition,
              verificationStatus: oldProfile.verificationStatus,
              ...(newCompanyId && { company: { connect: { id: newCompanyId } } }),
              ...getTimestamps(oldProfile),
            };
            await tx.hostProfile.create({ data: profileData });
            associatedDataMigratedCount++;
            logger.debug(`Migrated HostProfile for user ${newUserId}`);
          }

          // Migrate Education for HostProfile
          if (oldProfile.education) {
            // Check if education is an array and has items
            for (const oldEdu of oldProfile.education) {
              try {
                const newEducationData: Prisma.EducationCreateInput = {
                  userId: newUserId,
                  user: { connect: { id: newUserId } },
                  hostProfile: { connect: { userId: newUserId } },
                  university: oldEdu.university,
                  major: oldEdu.major,
                  degree: oldEdu.degree,
                  startDate: oldEdu.startDate ? new Date(oldEdu.startDate) : null,
                  endDate: oldEdu.endDate ? new Date(oldEdu.endDate) : null,
                  ...getTimestamps(oldEdu),
                };
                const existingEdu = await tx.education.findFirst({
                  where: {
                    userId: newUserId,
                    hostProfileUserId: newUserId,
                    university: oldEdu.university,
                    major: oldEdu.major,
                    degree: oldEdu.degree,
                  },
                });
                if (existingEdu) {
                  logger.debug(
                    `Education for host ${newUserId} (Uni: ${oldEdu.university}) already exists. Skipping.`
                  );
                } else {
                  await tx.education.create({ data: newEducationData });
                  associatedDataMigratedCount++;
                }
              } catch (eduError: any) {
                logger.error(
                  `Failed to migrate Education for HostProfile ${newUserId} (Old Edu ID: ${oldEdu.id}): ${eduError.message}`
                );
              }
            }
          }
          // Migrate PayoutInformation for HostProfile
          if (oldProfile.payoutInformation) {
            const oldPayout = oldProfile.payoutInformation;
            try {
              const existingPayout = await tx.payoutInformation.findUnique({
                where: { hostId: newUserId },
              });
              if (existingPayout) {
                logger.debug(`PayoutInformation for host ${newUserId} already exists. Skipping.`);
              } else {
                await tx.payoutInformation.create({
                  data: {
                    host: { connect: { userId: newUserId } },
                    accountOwnerName: oldPayout.accountOwnerName,
                    bankCode: oldPayout.bankCode,
                    branchCode: oldPayout.branchCode,
                    accountType: oldPayout.accountType,
                    accountNumber: oldPayout.accountNumber,
                    ...getTimestamps(oldPayout),
                  },
                });
                associatedDataMigratedCount++;
              }
            } catch (payoutError: any) {
              logger.error(
                `Failed to migrate PayoutInformation for HostProfile ${newUserId} (Old User ID: ${oldUser.id}): ${payoutError.message}`
              );
            }
          }
        } catch (hpError: any) {
          logger.error(
            `Failed to migrate HostProfile for user ${newUserId} (Old User ID: ${oldUser.id}): ${hpError.message}`
          );
        }
      }

      // Link User to Roles (Many-to-Many)
      if (oldUser.roles && oldUser.roles.length > 0) {
        const newRoleIdsToConnect = oldUser.roles
          .map((oldRole) => roleIdMap.get(oldRole.id))
          .filter((id): id is string => id !== undefined); // Type guard

        if (newRoleIdsToConnect.length > 0) {
          try {
            // Check existing roles for the user to prevent duplicate connections if script is re-run partially
            const currentUserRoles = await tx.user.findUnique({
              where: { id: newUserId },
              select: { roles: { select: { id: true } } },
            });
            const existingRoleIds = currentUserRoles?.roles.map((r) => r.id) || [];
            const rolesToActuallyConnect = newRoleIdsToConnect.filter((id) => !existingRoleIds.includes(id));

            if (rolesToActuallyConnect.length > 0) {
              await tx.user.update({
                where: { id: newUserId },
                data: {
                  roles: {
                    connect: rolesToActuallyConnect.map((id) => ({ id })),
                  },
                },
              });
              logger.debug(`Linked user ${newUserId} to ${rolesToActuallyConnect.length} new roles.`);
            } else {
              logger.debug(`User ${newUserId} already linked to necessary roles. No new roles to connect.`);
            }
          } catch (roleLinkError: any) {
            logger.error(
              `Failed to link roles for user ${newUserId} (Old User ID: ${oldUser.id}): ${roleLinkError.message}`
            );
          }
        }
      }
    } catch (userError: any) {
      logger.error(
        `Major error processing user (Old ID: ${oldUser.id}, Email: ${oldUser.email}): ${userError.message}`
      );
    }
  }

  logger.info(`User migration summary: 
    ${usersMigratedCount} users newly created.
    ${usersSkippedCount} users skipped (already existed, associated data still processed).
    Approximately ${associatedDataMigratedCount} associated records (accounts, profiles, etc.) processed/created.
    Total old users processed: ${oldUsers.length}.`);
  return userIdMap;
}

// --- Event and Associated Data Migration ---

async function migrateAdminsForEvent(
  tx: any,
  oldDb: OldPrismaClient,
  oldEventId: string,
  newEventId: string,
  userIdMap: Map<string, string>
): Promise<void> {
  logger.debug(`Migrating Admins for oldEventId ${oldEventId} to newEventId ${newEventId}...`);
  const oldAdmins = await oldDb.admin.findMany({ where: { eventId: oldEventId } });
  let migratedCount = 0;

  for (const oldAdmin of oldAdmins) {
    const newAdminHostId = userIdMap.get(oldAdmin.hostId);
    const newAddedById = userIdMap.get(oldAdmin.addedBy);

    if (!newAdminHostId) {
      logger.warn(
        `Admin for event ${newEventId} (Old Admin ID: ${oldAdmin.id}) - hostId ${oldAdmin.hostId} not found in userIdMap. Skipping.`
      );
      continue;
    }
    // addedBy might be a system user or an actual user. If not found, could be problematic or intentional.
    if (oldAdmin.addedBy && !newAddedById) {
      logger.warn(
        `Admin for event ${newEventId} (Old Admin ID: ${oldAdmin.id}) - addedBy user ${oldAdmin.addedBy} not found in userIdMap. Will use a placeholder or it might fail if DB requires it.`
      );
    }

    try {
      const existingAdmin = await tx.admin.findUnique({
        where: { eventId_hostId_unique: { eventId: newEventId, hostId: newAdminHostId } },
      });
      if (existingAdmin) {
        logger.debug(`Admin ${newAdminHostId} for event ${newEventId} already exists. Skipping.`);
        continue;
      }
      await tx.admin.create({
        data: {
          event: { connect: { id: newEventId } },
          host: { connect: { userId: newAdminHostId } },
          // Prisma schema requires 'addedBy' to be a string. If newAddedById is undefined, this will fail.
          // This implies 'addedBy' should always map to a valid User ID or needs a default system ID.
          addedBy: newAddedById || "SYSTEM_MIGRATED_ADMIN_ADDER", // Ensure this is a valid User ID or handle differently
          active: oldAdmin.active,
          ...getTimestamps(oldAdmin),
        },
      });
      migratedCount++;
      logger.debug(`Migrated Admin ${newAdminHostId} for event ${newEventId}.`);
    } catch (error: any) {
      logger.error(
        `Failed to migrate admin (Old ID: ${oldAdmin.id}, Old Host ID: ${oldAdmin.hostId}) for event ${newEventId}: ${error.message}`
      );
    }
  }
  logger.debug(`Finished migrating ${migratedCount} Admins for oldEventId ${oldEventId}.`);
}

interface TicketMigrationResult {
  ticketTypeIdMap: Map<string, string>;
  ticketIdMap: Map<string, string>;
}
async function migrateTicketTypesAndTicketsForEvent(
  tx: any,
  oldDb: OldPrismaClient,
  oldEventId: string,
  newEventId: string,
  userIdMap: Map<string, string>
): Promise<TicketMigrationResult> {
  logger.debug(
    `Migrating TicketTypes and Tickets for oldEventId ${oldEventId} to newEventId ${newEventId}...`
  );
  const ticketTypeIdMap = new Map<string, string>();
  const ticketIdMap = new Map<string, string>();
  let ticketTypesMigrated = 0;
  let ticketsMigrated = 0;

  const oldTicketTypes = await oldDb.ticketType.findMany({
    where: { eventId: oldEventId },
    include: { tickets: { include: { payment: true } } }, // Include tickets and their payments
  });

  for (const oldTT of oldTicketTypes) {
    try {
      // Simple duplicate check for TicketType (e.g., by name and eventId)
      const existingTT = await tx.ticketType.findFirst({
        where: { eventId: newEventId, name: oldTT.name },
      });
      let newTTId = "";
      if (existingTT) {
        newTTId = existingTT.id;
        logger.debug(
          `TicketType '${oldTT.name}' for event ${newEventId} already exists. Old ID: ${oldTT.id}, New ID: ${newTTId}`
        );
      } else {
        const newTT = await tx.ticketType.create({
          data: {
            event: { connect: { id: newEventId } },
            name: oldTT.name,
            description: oldTT.description,
            price: oldTT.price, // Prisma Decimal
            active: oldTT.active,
            stripeProductId: oldTT.stripeProductId,
            stripePriceId: oldTT.stripePriceId,
            quantity: oldTT.quantity,
            availableFrom: new Date(oldTT.availableFrom),
            availableUntil: new Date(oldTT.availableUntil),
            ...getTimestamps(oldTT),
          },
        });
        newTTId = newTT.id;
        ticketTypesMigrated++;
        logger.debug(`Migrated TicketType: ${newTT.name} (Old ID: ${oldTT.id}, New ID: ${newTTId})`);
      }
      ticketTypeIdMap.set(oldTT.id, newTTId);

      // Migrate Tickets for this TicketType
      for (const oldTicket of oldTT.tickets) {
        const newUserId = userIdMap.get(oldTicket.userId);
        if (!newUserId) {
          logger.warn(
            `Ticket (Old ID: ${oldTicket.id}) references old userId ${oldTicket.userId} not in userIdMap. Skipping this ticket.`
          );
          continue;
        }

        let newPaymentId = "";
        // Payment migration is complex. For now, assume payment exists or create a placeholder.
        // A robust solution would migrate payments first and provide a paymentIdMap.
        if (oldTicket.payment) {
          // Try to find or create payment - this is a simplification
          const oldPayment = oldTicket.payment;
          let existingPayment: any = null;

          // Only search by transactionId if it's not null
          if (oldPayment.transactionId) {
            existingPayment = await tx.payment.findUnique({
              where: { transactionId: oldPayment.transactionId },
            });
          }

          if (existingPayment) {
            newPaymentId = existingPayment.id;
          } else {
            try {
              const newPayment = await tx.payment.create({
                data: {
                  amount: oldPayment.amount,
                  currency: oldPayment.currency,
                  status: oldPayment.status as PaymentStatus,
                  paymentMethod: oldPayment.paymentMethod,
                  transactionId: oldPayment.transactionId,
                  ...getTimestamps(oldPayment),
                },
              });
              newPaymentId = newPayment.id;
            } catch (paymentError: any) {
              logger.error(
                `Failed to migrate payment for old ticket ${oldTicket.id}: ${paymentError.message}. Ticket will not have payment linked.`
              );
            }
          }
        } else {
          logger.warn(
            `Ticket (Old ID: ${oldTicket.id}) has no payment associated in old DB. paymentId will be missing.`
          );
          // This will likely cause an error if paymentId is required for Ticket creation without a direct relation setup
          // The schema shows paymentId is non-nullable on Ticket and has a relation.
          // For now, this ticket would fail to migrate if its payment cannot be resolved or is missing.
          // This indicates Ticket.paymentId should perhaps be nullable, or payments must be robustly handled.
          // Let's assume for this script that if oldTicket.paymentId exists but payment itself is not found, we cannot proceed with this ticket.
          // Given the new schema where Ticket.paymentId is required, we must have a valid newPaymentId.
          logger.error(
            `Ticket (Old ID: ${oldTicket.id}) requires a payment, but its old payment (ID: ${oldTicket.paymentId}) could not be migrated or found. Skipping ticket.`
          );
          continue;
        }
        if (!newPaymentId && oldTicket.paymentId) {
          // If old payment existed but couldn't be migrated
          logger.error(
            `Ticket (Old ID: ${oldTicket.id}) could not establish a new payment ID. Skipping ticket.`
          );
          continue;
        }

        try {
          // Duplicate check for Ticket (e.g. userId, eventId, ticketTypeId - or rely on qrCode if unique)
          // This is tricky without a solid business unique key for tickets beyond ID.
          // For simplicity, we assume new if IDs don't match or create based on old ID.
          // A safer check might be on qrCode if it's reliably unique and present.
          const existingTicket = oldTicket.qrCode
            ? await tx.ticket.findUnique({ where: { qrCode: oldTicket.qrCode } })
            : null;
          let newTicketId = "";

          if (
            existingTicket &&
            existingTicket.eventId === newEventId &&
            existingTicket.userId === newUserId
          ) {
            newTicketId = existingTicket.id;
            logger.debug(
              `Ticket for user ${newUserId}, event ${newEventId} (QR: ${oldTicket.qrCode}) already exists. Old ID: ${oldTicket.id}, New ID: ${newTicketId}`
            );
          } else if (existingTicket) {
            logger.warn(
              `Ticket with QR ${oldTicket.qrCode} exists but with different user/event. This might be an issue. Skipping for old ticket ${oldTicket.id}`
            );
            continue;
          } else {
            const newTicket = await tx.ticket.create({
              data: {
                ticketType: { connect: { id: newTTId } },
                userId: newUserId, // Direct userId field, no relation
                event: { connect: { id: newEventId } },
                payment: { connect: { id: newPaymentId } },
                status: oldTicket.status as TicketStatus,
                active: oldTicket.active,
                qrCode: oldTicket.qrCode,
                checkedInAt: oldTicket.checkedInAt ? new Date(oldTicket.checkedInAt) : null,
                ...getTimestamps(oldTicket),
              },
            });
            newTicketId = newTicket.id;
            ticketsMigrated++;
          }
          ticketIdMap.set(oldTicket.id, newTicketId);
          logger.debug(
            `Migrated Ticket (Old ID: ${oldTicket.id}, New ID: ${newTicketId}) for user ${newUserId}`
          );
        } catch (ticketError: any) {
          logger.error(
            `Failed to migrate ticket (Old ID: ${oldTicket.id}) for TT ${newTTId}: ${ticketError.message}`
          );
        }
      }
    } catch (ttError: any) {
      logger.error(
        `Failed to migrate ticket type (Old ID: ${oldTT.id}) for event ${newEventId}: ${ttError.message}`
      );
    }
  }
  logger.debug(
    `Finished migrating ${ticketTypesMigrated} TicketTypes and ${ticketsMigrated} Tickets for oldEventId ${oldEventId}.`
  );
  return { ticketTypeIdMap, ticketIdMap };
}

interface FormAndQuestionsMigrationResult {
  newFormIdIfAny: string | undefined;
  questionIdMap: Map<string, string>;
}
async function migrateFormAndQuestionsForEvent(
  tx: any,
  oldDb: OldPrismaClient,
  oldEventId: string,
  newEventId: string
): Promise<FormAndQuestionsMigrationResult> {
  logger.debug(`Migrating Form and Questions for oldEventId ${oldEventId} to newEventId ${newEventId}...`);
  const questionIdMap = new Map<string, string>();
  let newFormIdIfAny: string | undefined = undefined;
  let questionsMigrated = 0;

  const oldForm = await oldDb.form.findUnique({
    where: { eventId: oldEventId },
    include: { questions: true },
  });

  if (oldForm) {
    try {
      const existingForm = await tx.form.findUnique({ where: { eventId: newEventId } });
      if (existingForm) {
        newFormIdIfAny = existingForm.id;
        logger.debug(
          `Form for event ${newEventId} already exists (ID: ${newFormIdIfAny}). Skipping creation.`
        );
      } else {
        const newForm = await tx.form.create({
          data: {
            event: { connect: { id: newEventId } },
            name: oldForm.name,
            description: oldForm.description,
            termsOfUse: oldForm.termsOfUse,
            privacyPolicy: oldForm.privacyPolicy,
            ...getTimestamps(oldForm),
          },
        });
        newFormIdIfAny = newForm.id;
        logger.debug(
          `Migrated Form for event ${newEventId} (Old ID: ${oldForm.id}, New ID: ${newFormIdIfAny})`
        );
      }

      // Migrate Questions for this Form
      if (newFormIdIfAny && oldForm.questions) {
        for (const oldQuestion of oldForm.questions) {
          try {
            const existingQuestion = await tx.question.findFirst({
              where: {
                formId: newFormIdIfAny,
                questionText: oldQuestion.questionText,
                questionType: oldQuestion.questionType as QuestionType,
              },
            });
            let newQuestionId = "";
            if (existingQuestion) {
              newQuestionId = existingQuestion.id;
              logger.debug(
                `Question "${oldQuestion.questionText}" for form ${newFormIdIfAny} already exists. Old ID: ${oldQuestion.id}, New ID: ${newQuestionId}`
              );
            } else {
              const newQuestion = await tx.question.create({
                data: {
                  form: { connect: { id: newFormIdIfAny } },
                  questionText: oldQuestion.questionText,
                  questionType: oldQuestion.questionType as QuestionType,
                  isBasic: oldQuestion.isBasic,
                  isPrivate: oldQuestion.isPrivate,
                  options: oldQuestion.options || Prisma.JsonNull,
                  isRequired: oldQuestion.isRequired,
                  order: oldQuestion.order,
                  ...getTimestamps(oldQuestion),
                },
              });
              newQuestionId = newQuestion.id;
              questionsMigrated++;
            }
            questionIdMap.set(oldQuestion.id, newQuestionId);
          } catch (qError: any) {
            logger.error(
              `Failed to migrate question (Old ID: ${oldQuestion.id}) for form ${newFormIdIfAny}: ${qError.message}`
            );
          }
        }
      }
    } catch (fError: any) {
      logger.error(
        `Failed to migrate form for event ${newEventId} (Old Form ID: ${oldForm.id}): ${fError.message}`
      );
    }
  } else {
    logger.debug(`No form found for oldEventId ${oldEventId}.`);
  }
  logger.debug(
    `Finished migrating form (if any) and ${questionsMigrated} Questions for oldEventId ${oldEventId}.`
  );
  return { newFormIdIfAny, questionIdMap };
}

async function migrateEventParticipantsForEvent(
  tx: any,
  oldDb: OldPrismaClient,
  oldEventId: string,
  newEventId: string,
  userIdMap: Map<string, string>,
  ticketIdMap: Map<string, string>,
  newFormIdIfAny: string | undefined,
  questionIdMap: Map<string, string>
): Promise<Map<string, string>> {
  // Returns eventParticipantIdMap: oldEP.id -> newEP.id
  logger.info(`Migrating EventParticipants for oldEventId ${oldEventId} to newEventId ${newEventId}...`);
  const eventParticipantIdMap = new Map<string, string>();
  let participantsMigrated = 0;
  let participantsSkipped = 0;

  const oldParticipants = await oldDb.eventParticipant.findMany({
    where: { eventId: oldEventId },
    include: {
      notes: true,
      checkInOutRecord: true,
      formSubmission: { include: { answers: true } },
      // user: true, // Not strictly needed if we rely on userId and userIdMap
      // ticket: true, // Not strictly needed if we rely on ticketId and ticketIdMap
    },
  });

  for (const oldEP of oldParticipants) {
    let newUserId: string | undefined = undefined;
    if (oldEP.userId) {
      newUserId = userIdMap.get(oldEP.userId);
      if (!newUserId) {
        logger.warn(
          `EventParticipant (Old ID: ${oldEP.id}) for event ${newEventId} references old userId ${oldEP.userId} not in userIdMap. Treating as guest or skipping.`
        );
        // Decide: skip, or try to create as guest if guestEmail is present?
        // For now, if userId was present but not mapped, it's an issue.
        if (!oldEP.guestEmail) {
          // If it was supposed to be a user, but user not found, and no guest fallback
          participantsSkipped++;
          continue;
        }
      }
    }

    const newTicketId = oldEP.ticketId ? ticketIdMap.get(oldEP.ticketId) : undefined;
    if (oldEP.ticketId && !newTicketId) {
      logger.warn(
        `EventParticipant (Old ID: ${oldEP.id}) for event ${newEventId} references old ticketId ${oldEP.ticketId} not in ticketIdMap. Ticket link will be missing.`
      );
    }

    let newEventParticipantId = "";
    try {
      // Check for existing EventParticipant
      let existingEP: any = null;
      if (newUserId) {
        existingEP = await tx.eventParticipant.findUnique({
          where: { event_user_unique: { eventId: newEventId, userId: newUserId } },
        });
      } else if (oldEP.guestEmail) {
        // Guest participant
        existingEP = await tx.eventParticipant.findUnique({
          where: { event_guest_email_unique: { eventId: newEventId, guestEmail: oldEP.guestEmail } },
        });
      }

      if (existingEP) {
        newEventParticipantId = existingEP.id;
        eventParticipantIdMap.set(oldEP.id, newEventParticipantId);
        participantsSkipped++;
        logger.debug(
          `EventParticipant (User: ${newUserId || oldEP.guestEmail}, Event: ${newEventId}) already exists (New ID: ${newEventParticipantId}). Old EP ID: ${oldEP.id}. Skipping creation, associated data might still be processed if needed (not implemented here).`
        );
        // If we need to update existing EPs or their sub-data, logic would go here.
        // For now, if EP exists, we assume sub-data also handled or doesn't need merging.
        // Continue to next participant to avoid re-migrating sub-entities for an already existing EP.
        continue;
      }

      const participantData: Prisma.EventParticipantCreateInput = {
        event: { connect: { id: newEventId } },
        ...(newUserId && { user: { connect: { id: newUserId } } }),
        guestName: oldEP.guestName,
        guestEmail: oldEP.guestEmail,
        ...(newTicketId && { ticket: { connect: { id: newTicketId } } }),
        status: oldEP.status as ParticipantStatus,
        checkInTime: oldEP.checkInTime ? new Date(oldEP.checkInTime) : null,
        checkInMethod: oldEP.checkInMethod as CheckInMethod | null,
        ...getTimestamps(oldEP),
      };

      const newEP = await tx.eventParticipant.create({ data: participantData });
      newEventParticipantId = newEP.id;
      eventParticipantIdMap.set(oldEP.id, newEventParticipantId);
      participantsMigrated++;
      logger.debug(
        `Migrated EventParticipant (Old ID: ${oldEP.id}, New ID: ${newEventParticipantId}) for event ${newEventId}`
      );

      // Migrate Notes for this EventParticipant
      for (const oldNote of oldEP.notes) {
        try {
          // Assumption: oldNote.writtenBy is a userId (host or another participant's user)
          // This needs validation. If writtenBy can be an oldEP.id for guests, this is insufficient.
          const newWrittenById = userIdMap.get(oldNote.writtenBy);
          if (!newWrittenById && oldNote.writtenBy) {
            // If there was a writer ID but it's not mapped.
            logger.warn(
              `Note (Old ID: ${oldNote.id}) for EP ${newEventParticipantId} has unmapped writer ID ${oldNote.writtenBy}. Storing old ID or placeholder.`
            );
            // Storing the old ID directly in newWrittenById might violate foreign key if 'writtenBy' is a relation.
            // The schema says 'String', so it might be okay to store the old ID if it's not a strict FK to User.
            // For now, this note might not be correctly attributed.
          }

          await tx.note.create({
            data: {
              EventParticipant: { connect: { id: newEventParticipantId } },
              active: oldNote.active,
              noteText: oldNote.noteText,
              type: oldNote.type as NoteType,
              writtenBy: newWrittenById || oldNote.writtenBy, // Fallback to old ID if not mapped; adjust if 'writtenBy' must be valid new User ID.
              ...getTimestamps(oldNote),
            },
          });
        } catch (noteError: any) {
          logger.error(
            `Failed to migrate Note (Old ID: ${oldNote.id}) for EP ${newEventParticipantId}: ${noteError.message}`
          );
        }
      }

      // Migrate CheckInOutRecords for this EventParticipant
      for (const oldCheckInOut of oldEP.checkInOutRecord) {
        try {
          await tx.checkInOutRecord.create({
            data: {
              participant: { connect: { id: newEventParticipantId } },
              actionType: oldCheckInOut.actionType as CheckInOutActionType,
              actionTime: new Date(oldCheckInOut.actionTime),
              method: oldCheckInOut.method as CheckInMethod | null,
              ...getTimestamps(oldCheckInOut),
            },
          });
        } catch (cioError: any) {
          logger.error(
            `Failed to migrate CheckInOutRecord (Old ID: ${oldCheckInOut.id}) for EP ${newEventParticipantId}: ${cioError.message}`
          );
        }
      }

      // Migrate FormSubmissions for this EventParticipant
      if (newFormIdIfAny && oldEP.formSubmission.length > 0) {
        for (const oldSubmission of oldEP.formSubmission) {
          let newFormSubmissionId = "";
          try {
            // Check for existing submission (e.g., by participantId and formId)
            const existingSubmission = await tx.formSubmission.findFirst({
              where: { participantId: newEventParticipantId, formId: newFormIdIfAny },
            });

            if (existingSubmission) {
              newFormSubmissionId = existingSubmission.id;
              logger.debug(
                `FormSubmission for EP ${newEventParticipantId}, Form ${newFormIdIfAny} already exists. ID: ${newFormSubmissionId}. Skipping creation.`
              );
            } else {
              const newSubmission = await tx.formSubmission.create({
                data: {
                  form: { connect: { id: newFormIdIfAny } },
                  eventParticipant: { connect: { id: newEventParticipantId } },
                  agreeLegal: oldSubmission.agreeLegal,
                  ...getTimestamps(oldSubmission),
                },
              });
              newFormSubmissionId = newSubmission.id;
            }

            // Migrate FormAnswers for this FormSubmission
            if (newFormSubmissionId && oldSubmission.answers) {
              for (const oldAnswer of oldSubmission.answers) {
                const newQuestionId = questionIdMap.get(oldAnswer.questionId);
                if (!newQuestionId) {
                  logger.warn(
                    `FormAnswer (Old ID: ${oldAnswer.id}) for Submission ${newFormSubmissionId} references old questionId ${oldAnswer.questionId} not in questionIdMap. Skipping answer.`
                  );
                  continue;
                }
                try {
                  // Check for existing answer (e.g. by submissionId and questionId)
                  const existingAnswer = await tx.formAnswer.findFirst({
                    where: { formSubmissionId: newFormSubmissionId, questionId: newQuestionId },
                  });
                  if (existingAnswer) {
                    logger.debug(
                      `FormAnswer for Sub ${newFormSubmissionId}, Q ${newQuestionId} already exists. Skipping.`
                    );
                    continue;
                  }
                  await tx.formAnswer.create({
                    data: {
                      formSubmission: { connect: { id: newFormSubmissionId } },
                      question: { connect: { id: newQuestionId } },
                      answer: oldAnswer.answer || Prisma.JsonNull,
                      ...getTimestamps(oldAnswer),
                    },
                  });
                } catch (ansError: any) {
                  logger.error(
                    `Failed to migrate FormAnswer (Old ID: ${oldAnswer.id}) for Submission ${newFormSubmissionId}: ${ansError.message}`
                  );
                }
              }
            }
          } catch (subError: any) {
            logger.error(
              `Failed to migrate FormSubmission (Old ID: ${oldSubmission.id}) for EP ${newEventParticipantId}: ${subError.message}`
            );
          }
        }
      }
    } catch (epError: any) {
      logger.error(
        `Failed to migrate EventParticipant (Old ID: ${oldEP.id}) or its associated data for event ${newEventId}: ${epError.message}`
      );
    }
  }

  logger.info(
    `EventParticipants migration for event ${newEventId}: ${participantsMigrated} created, ${participantsSkipped} skipped. Total processed: ${oldParticipants.length}.`
  );
  return eventParticipantIdMap;
}

async function migrateEventsAndAssociatedData(
  tx: any,
  oldDb: OldPrismaClient,
  targetAppId: string,
  userIdMap: Map<string, string>
) {
  logger.info("Migrating Events and their associated data...");
  let eventsMigratedCount = 0;
  let eventsSkippedCount = 0;
  const eventIdMap = new Map<string, string>(); // oldEventId -> newEventId

  const oldEvents = await oldDb.event.findMany({
    // No deep includes here; fetch related data iteratively per event
  });

  logger.info(`Found ${oldEvents.length} events in old database to process.`);

  for (const oldEvent of oldEvents) {
    let newEventId = "";
    try {
      const newHostId = userIdMap.get(oldEvent.hostId);
      if (!newHostId) {
        logger.warn(
          `Event (Old ID: ${oldEvent.id}, Name: ${oldEvent.name}) references old hostId ${oldEvent.hostId} which was not mapped. Skipping this event.`
        );
        eventsSkippedCount++;
        continue;
      }

      const existingEvent = await tx.event.findFirst({
        where: {
          name: oldEvent.name,
          hostId: newHostId,
          startDatetime: new Date(oldEvent.startDatetime),
        },
      });

      if (existingEvent) {
        newEventId = existingEvent.id;
        logger.info(
          `Event "${oldEvent.name}" (Old ID: ${oldEvent.id}) seems to exist (New ID: ${newEventId}). Skipping core creation, migrating related data.`
        );
        eventsSkippedCount++;
      } else {
        const eventData: Prisma.EventCreateInput = {
          app: { connect: { id: targetAppId } },
          host: { connect: { userId: newHostId } },
          name: oldEvent.name,
          description: oldEvent.description,
          bannerImageUrl: oldEvent.bannerImageUrl,
          startDatetime: new Date(oldEvent.startDatetime),
          endDatetime: new Date(oldEvent.endDatetime),
          timezone: oldEvent.timezone,
          location: oldEvent.location || Prisma.JsonNull,
          tags: oldEvent.tags,
          status: oldEvent.status as EventStatus,
          ...getTimestamps(oldEvent),
        };

        const newEvent = await tx.event.create({ data: eventData });
        newEventId = newEvent.id;
        eventsMigratedCount++;
        logger.info(`Migrated Event: "${newEvent.name}" (Old ID: ${oldEvent.id}, New ID: ${newEvent.id})`);
      }
      eventIdMap.set(oldEvent.id, newEventId);

      // Migrate associated data for this event
      await migrateAdminsForEvent(tx, oldDb, oldEvent.id, newEventId, userIdMap);

      const ticketMigrationResult = await migrateTicketTypesAndTicketsForEvent(
        tx,
        oldDb,
        oldEvent.id,
        newEventId,
        userIdMap
      );

      const formMigrationResult = await migrateFormAndQuestionsForEvent(tx, oldDb, oldEvent.id, newEventId);

      await migrateEventParticipantsForEvent(
        tx,
        oldDb,
        oldEvent.id,
        newEventId,
        userIdMap,
        ticketMigrationResult.ticketIdMap,
        formMigrationResult.newFormIdIfAny,
        formMigrationResult.questionIdMap
      );
    } catch (error: any) {
      logger.error(
        `Failed to migrate event or its associated data (Old ID: ${oldEvent.id}, Name: ${oldEvent.name}): ${error.message}`
      );
    }
  }
  logger.info(
    `Events migration: ${eventsMigratedCount} created, ${eventsSkippedCount} skipped. Total old events processed: ${oldEvents.length}.`
  );
  // return eventIdMap; // If needed by subsequent migrations
}

async function migratePolicyAndTerms(tx: any, oldDb: OldPrismaClient, targetAppId: string) {
  logger.info("Migrating PolicyAndTerms...");
  let DBMigrationCounter = 0;
  let processedCounter = 0;

  const oldPolicies = await oldDb.policyAndTerms.findMany();
  processedCounter = oldPolicies.length;

  for (const oldPolicy of oldPolicies) {
    try {
      const existingPolicy = await tx.policyAndTerms.findFirst({
        where: {
          appId: targetAppId,
          type: oldPolicy.type,
          name: oldPolicy.name,
        },
      });

      if (existingPolicy) {
        logger.debug(
          `Policy/Term '${oldPolicy.name}' (Type: ${oldPolicy.type}) for app ${targetAppId} already exists. Skipping.`
        );
      } else {
        const policyData: Prisma.PolicyAndTermsCreateInput = {
          app: { connect: { id: targetAppId } },
          type: oldPolicy.type,
          name: oldPolicy.name,
          description: oldPolicy.description,
          ...getTimestamps(oldPolicy),
        };
        await tx.policyAndTerms.create({ data: policyData });
        DBMigrationCounter++;
        logger.debug(`Migrated Policy/Term: ${oldPolicy.name} (Old ID: ${oldPolicy.id})`);
      }
    } catch (error: any) {
      logger.error(
        `Failed to migrate policy/term (Old ID: ${oldPolicy.id}, Name: ${oldPolicy.name}): ${error.message}`
      );
    }
  }
  logger.info(
    `PolicyAndTerms migration: ${DBMigrationCounter} created, ${processedCounter - DBMigrationCounter} skipped. Total processed: ${processedCounter}.`
  );
}

// --- Main Migration Function ---
async function main() {
  logger.info("Starting data migration script...");

  const appName = "event"; // Or your target app name
  // Upsert App. This can be outside or the first step inside the transaction.
  // Let's keep it outside as a prerequisite.
  const app = await newPrisma.app.upsert({
    where: { name: appName },
    update: {},
    create: { name: appName },
  });
  const targetAppId = app.id;
  logger.info(`Ensured App "${appName}" exists with ID: ${targetAppId}`);

  // Declare maps here to make them accessible after the transaction for logging
  let roleIdMap: Map<string, string> | undefined;
  let companyIdMap: Map<string, string> | undefined;
  let userIdMap: Map<string, string> | undefined;

  try {
    await newPrisma.$transaction(async (tx) => {
      logger.info("Main migration transaction started.");

      roleIdMap = await migrateRoles(tx, oldPrisma);
      companyIdMap = await migrateCompanies(tx, oldPrisma);
      // Pass the already populated roleIdMap and companyIdMap
      userIdMap = await migrateUsersAndAssociatedData(tx, oldPrisma, roleIdMap, companyIdMap);

      await migrateEventsAndAssociatedData(tx, oldPrisma, targetAppId, userIdMap);
      await migratePolicyAndTerms(tx, oldPrisma, targetAppId);

      // TODO: Migrate other entities like Favorites
      logger.warn("Migration for Favorites is not implemented in this script version.");
      logger.info("All operations within transaction completed successfully.");
    });

    logger.info("\nData migration script finished.");
    // Log map sizes if they were successfully populated
    if (roleIdMap && companyIdMap && userIdMap) {
      logger.info("Summary of ID Maps created:");
      logger.info(`Role ID Map entries: ${roleIdMap.size}`);
      logger.info(`Company ID Map entries: ${companyIdMap.size}`);
      logger.info(`User ID Map entries: ${userIdMap.size}`);
    } else {
      logger.warn(
        "ID maps were not fully populated, possibly due to an error before their assignment or transaction rollback."
      );
    }
  } catch (error) {
    logger.error("Migration failed within the transaction:", error);
    // The transaction will be rolled back automatically by Prisma on error.
    // Rethrow or handle as per desired script behavior on failure
    throw error; // Rethrow to ensure main().catch() below handles it
  }
}

main()
  .catch(async (e) => {
    logger.error("Unhandled error in migration script:", e);
    process.exit(1);
  })
  .finally(async () => {
    await newPrisma.$disconnect();
    await oldPrisma.$disconnect();
    logger.info("Prisma clients disconnected.");
  });
