"use client";

import React, { useState, useEffect } from "react";
import { Controller, UseFormReturn } from "react-hook-form";
import { format } from "date-fns";

import { But<PERSON> } from "@meeeetup/ui/button";
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "@meeeetup/ui/dialog";
import { Input } from "@meeeetup/ui/input";
import { Checkbox } from "@meeeetup/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@meeeetup/ui/select";
import { Pencil, Trash2, Plus } from "lucide-react";
import { cn } from "@meeeetup/ui";
import { useTranslations } from "next-intl";

import { FormSchemaType } from "./types";

// Month options for the select
const MONTHS_KEYS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

// Generic interfaces
export interface GenericFormItem {
  id: string;
  startDate: string;
  endDate?: string;
  [key: string]: any;
}

export interface FormField {
  id: string;
  label: string;
  placeholder: string;
  required?: boolean;
  type?: "text" | "url";
  value: string;
  onChange: (value: string) => void;
}

export interface GenericFormConfig<T extends GenericFormItem> {
  // Item display configuration
  getItemTitle: (item: T) => string;
  getItemSubtitle: (item: T) => string;
  getItemExtraInfo?: (item: T) => React.ReactNode;

  // Dialog configuration
  dialogTitle: {
    add: string;
    edit: string;
  };

  // Form fields configuration
  getFormFields: (item: T, onChange: (updates: Partial<T>) => void) => FormField[];

  // Validation
  isValid: (item: T) => boolean;

  // Current status configuration
  currentStatusConfig: {
    checkboxLabel: string;
    endDateLabel: string;
  };

  // Empty state messages
  emptyStateMessage: string;
  addButtonText: string;
  requiredErrorMessage: string;
}

// Generic item display component
interface GenericItemProps<T extends GenericFormItem> {
  item: T;
  config: GenericFormConfig<T>;
  onEdit: () => void;
  onRemove: () => void;
}

function GenericItem<T extends GenericFormItem>({ item, config, onEdit, onRemove }: GenericItemProps<T>) {
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    try {
      return format(new Date(dateString), "MMM yyyy");
    } catch (e) {
      return dateString;
    }
  };

  return (
    <div className="border rounded-md p-4 mb-2 bg-white hover:bg-gray-50 transition-colors">
      <div className="flex justify-between items-center">
        <div className="flex-1">
          <h3 className="font-medium text-base">{config.getItemTitle(item)}</h3>
          <p className="text-sm text-gray-600">{config.getItemSubtitle(item)}</p>
          <div className="flex items-center gap-2 mt-1">
            <span className="text-xs text-gray-500">
              {formatDate(item.startDate)} - {item.endDate ? formatDate(item.endDate) : "Present"}
            </span>
            {config.getItemExtraInfo?.(item)}
          </div>
        </div>
        <div className="flex gap-1">
          <button
            type="button"
            onClick={onEdit}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors">
            <Pencil className="h-4 w-4" />
            <span className="sr-only">Edit</span>
          </button>
          <button
            type="button"
            onClick={onRemove}
            className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors">
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">Remove</span>
          </button>
        </div>
      </div>
    </div>
  );
}

// Date parsing utilities
const parseDateFromString = (dateString: string) => {
  try {
    const date = new Date(dateString);
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear().toString();
    return { month, year };
  } catch (e) {
    return { month: "", year: "" };
  }
};

const createDateFromMonthYear = (month: string, year: string) => {
  if (!month || !year) return "";
  return new Date(parseInt(year), parseInt(month) - 1, 1).toISOString();
};

// Main generic form component
export interface GenericFormListProps<T extends GenericFormItem> {
  formField: string;
  form: UseFormReturn<FormSchemaType>;
  label: string;
  required?: boolean;
  config: GenericFormConfig<T>;
  createNewItem: () => T;
}

export function GenericFormList<T extends GenericFormItem>({
  formField,
  form,
  label,
  required = false,
  config,
  createNewItem,
}: GenericFormListProps<T>) {
  const t = useTranslations("EventRegistration");
  const MONTHS = MONTHS_KEYS.map((month, index) => ({
    value: (index + 1).toString().padStart(2, "0"),
    label: t(month),
  }));

  const [items, setItems] = useState<T[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [tempItem, setTempItem] = useState<T>(createNewItem());
  const [isCurrent, setIsCurrent] = useState(false);

  // Date state
  const [startMonth, setStartMonth] = useState("");
  const [startYear, setStartYear] = useState("");
  const [endMonth, setEndMonth] = useState("");
  const [endYear, setEndYear] = useState("");

  const resetTempItem = () => {
    setTempItem(createNewItem());
    setIsCurrent(false);
    setStartMonth("");
    setStartYear("");
    setEndMonth("");
    setEndYear("");
  };

  const updateFormValue = (updatedItems: T[]) => {
    const jsonValue = JSON.stringify(updatedItems);
    form.setValue(formField, jsonValue);
  };

  // Update tempItem when date fields change
  useEffect(() => {
    if (startMonth && startYear) {
      const startDate = createDateFromMonthYear(startMonth, startYear);
      updateTempItem({ startDate } as Partial<T>);
    }
  }, [startMonth, startYear]);

  useEffect(() => {
    if (!isCurrent && endMonth && endYear) {
      const endDate = createDateFromMonthYear(endMonth, endYear);
      updateTempItem({ endDate } as Partial<T>);
    } else if (isCurrent) {
      updateTempItem({ endDate: undefined } as Partial<T>);
    }
  }, [endMonth, endYear, isCurrent]);

  const handleAdd = () => {
    const newItem = {
      ...tempItem,
      id: `item-${Date.now()}`,
    };

    const updatedItems = [...items, newItem];
    setItems(updatedItems);
    updateFormValue(updatedItems);
    resetTempItem();
    setIsDialogOpen(false);
  };

  const handleUpdate = () => {
    if (editIndex === null) return;

    const updatedItems = [...items];
    updatedItems[editIndex] = { ...tempItem };

    setItems(updatedItems);
    updateFormValue(updatedItems);
    setEditIndex(null);
    resetTempItem();
    setIsDialogOpen(false);
    setIsEditing(false);
  };

  const handleRemove = (index: number) => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
    updateFormValue(updatedItems);
  };

  const handleEdit = (index: number) => {
    setIsEditing(true);
    setEditIndex(index);
    const itemToEdit = items[index];
    if (itemToEdit) {
      setTempItem({ ...itemToEdit });
      setIsCurrent(!itemToEdit.endDate);

      // Parse and set date fields
      const startDateParts = parseDateFromString(itemToEdit.startDate);
      setStartMonth(startDateParts.month);
      setStartYear(startDateParts.year);

      if (itemToEdit.endDate) {
        const endDateParts = parseDateFromString(itemToEdit.endDate);
        setEndMonth(endDateParts.month);
        setEndYear(endDateParts.year);
      }
    }
    setIsDialogOpen(true);
  };

  const updateTempItem = (updates: Partial<T>) => {
    setTempItem((prev) => ({ ...prev, ...updates }));
  };

  // Parse form value when it changes (e.g., from initial autofill)
  useEffect(() => {
    const currentValue = form.getValues(formField);
    if (currentValue && typeof currentValue === "string" && currentValue.startsWith("[")) {
      try {
        const parsedItems = JSON.parse(currentValue);
        if (Array.isArray(parsedItems) && parsedItems.length > 0) {
          setItems(parsedItems);
        }
      } catch (e) {
        console.error("Failed to parse form data:", e);
      }
    }
  }, [form.getValues(formField)]);

  const formFields = config.getFormFields(tempItem, updateTempItem);

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <label className="block text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <Button
          type="button"
          variant="outline"
          size="small"
          onClick={() => {
            resetTempItem();
            setIsEditing(false);
            setIsDialogOpen(true);
          }}
          className="flex items-center gap-1">
          <Plus className="h-3 w-3" />
          <span>{t("Add")}</span>
        </Button>
      </div>

      <Controller
        name={formField}
        control={form.control}
        defaultValue=""
        render={({ field, fieldState }) => (
          <div>
            {items.length > 0 ? (
              <div className="space-y-2 max-h-[300px] overflow-y-auto pr-1">
                {items.map((item, index) => (
                  <GenericItem
                    key={item.id}
                    item={item}
                    config={config}
                    onEdit={() => handleEdit(index)}
                    onRemove={() => handleRemove(index)}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-6 border border-dashed rounded-md bg-gray-50">
                <p className="text-sm text-gray-500 mb-2">{config.emptyStateMessage}</p>
                <Button
                  type="button"
                  variant="link"
                  className="text-sm flex items-center gap-1 mx-auto"
                  onClick={() => {
                    resetTempItem();
                    setIsDialogOpen(true);
                  }}>
                  <Plus className="h-3 w-3" />
                  <span>{config.addButtonText}</span>
                </Button>
              </div>
            )}

            {fieldState.error && <p className="text-sm text-red-500 mt-1">{fieldState.error.message}</p>}
            {required && items.length === 0 && !fieldState.error && (
              <p className="text-sm text-red-500 mt-1">{config.requiredErrorMessage}</p>
            )}
          </div>
        )}
      />

      {/* Add/Edit Dialog */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            resetTempItem();
            setIsEditing(false);
          }
          setIsDialogOpen(open);
        }}>
        <DialogContent className="sm:max-w-[450px]">
          <DialogTitle className="text-center">
            {isEditing ? config.dialogTitle.edit : config.dialogTitle.add}
          </DialogTitle>

          <div className="space-y-4 py-2">
            {/* Dynamic form fields */}
            {formFields.map((field) => (
              <div key={field.id} className="space-y-2">
                <label htmlFor={field.id} className="text-sm font-medium">
                  {field.label} {field.required && <span className="text-red-500">*</span>}
                </label>
                <Input
                  id={field.id}
                  type={field.type || "text"}
                  value={field.value}
                  onChange={(e) => field.onChange(e.target.value)}
                  placeholder={field.placeholder}
                  className="w-full"
                />
              </div>
            ))}

            {/* Start Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {t("Start Date")} <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Select value={startMonth} onValueChange={setStartMonth}>
                    <SelectTrigger className="py-6">
                      <SelectValue placeholder="Month" />
                    </SelectTrigger>
                    <SelectContent>
                      {MONTHS.map((month) => (
                        <SelectItem key={month.value} value={month.value}>
                          {month.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Input
                    type="number"
                    placeholder="Year"
                    value={startYear}
                    onChange={(e) => setStartYear(e.target.value)}
                    min="1950"
                    max="2050"
                  />
                </div>
              </div>
            </div>

            {/* End Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium">{config.currentStatusConfig.endDateLabel}</label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Select value={isCurrent ? "" : endMonth} onValueChange={setEndMonth} disabled={isCurrent}>
                    <SelectTrigger className="py-6">
                      <SelectValue placeholder={isCurrent ? "Present" : "Month"} />
                    </SelectTrigger>
                    <SelectContent>
                      {MONTHS.map((month) => (
                        <SelectItem key={month.value} value={month.value}>
                          {month.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Input
                    type="number"
                    placeholder={isCurrent ? "Present" : "Year"}
                    value={isCurrent ? "" : endYear}
                    onChange={(e) => setEndYear(e.target.value)}
                    min="1950"
                    max="2050"
                    disabled={isCurrent}
                  />
                </div>
              </div>
            </div>

            {/* Current Status Checkbox */}
            <div className="flex items-center space-x-2 pt-1">
              <Checkbox
                id="current-status"
                checked={isCurrent}
                onCheckedChange={(checked) => {
                  setIsCurrent(!!checked);
                  if (checked) {
                    setEndMonth("");
                    setEndYear("");
                  }
                }}
              />
              <label htmlFor="current-status" className="text-sm">
                {config.currentStatusConfig.checkboxLabel}
              </label>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsDialogOpen(false);
                }}>
                {t("Cancel")}
              </Button>
              <Button
                type="button"
                onClick={isEditing ? handleUpdate : handleAdd}
                disabled={!config.isValid(tempItem)}>
                {isEditing ? t("Update") : t("Add")}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
