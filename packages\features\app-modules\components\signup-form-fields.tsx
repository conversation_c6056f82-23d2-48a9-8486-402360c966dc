"use client";

import { <PERSON>, Controller, FieldErrors, UseFormHandleSubmit, UseFormRegister } from "react-hook-form";
import Link from "next/link";
import { z } from "zod";
import { useTranslations } from "next-intl";

import { Input, PasswordInput, Button, Checkbox } from "@meeeetup/ui";
import GoogleButton from "@/app/(auth)/_components/oauth-buttons/google";
import { signupSchema } from "@/schema/auth/signup"; // Import the schema

// Use the schema to infer the form data type
export type SignupFormData = z.infer<typeof signupSchema> & {
  termsAccepted: boolean; // Keep termsAccepted as it's specific to the UI form
};

interface SignupFormFieldsProps {
  loginType: "organizer" | "participant";
  handleGoogleLogin: (loginType: "organizer" | "participant") => Promise<void>;
  // Use the SignupFormData type for onSubmit data as it includes termsAccepted
  onSubmit: (data: SignupFormData) => void;
  register: UseFormRegister<SignupFormData>;
  handleSubmit: UseFormHandleSubmit<SignupFormData>;
  control: Control<SignupFormData, any>;
  errors: FieldErrors<SignupFormData>;
  isPending: boolean;
  t: ReturnType<typeof useTranslations>;
  hideGoogleButton?: boolean;
}

export function SignupFormFields({
  loginType,
  handleGoogleLogin,
  onSubmit,
  register,
  handleSubmit,
  control,
  errors,
  isPending,
  t,
  hideGoogleButton = false,
}: SignupFormFieldsProps) {
  return (
    <div className="w-full flex flex-col gap-4">
      {!hideGoogleButton && (
        <>
          <GoogleButton
            onClick={async () => {
              await handleGoogleLogin(loginType);
            }}
          />

          <div className="flex items-center my-6">
            <div className="flex-grow h-px bg-gray-200"></div>
            <span className="mx-4 text-sm text-gray-500">{t("orContinueWith")}</span>
            <div className="flex-grow h-px bg-gray-200"></div>
          </div>
        </>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            {t("email")}
          </label>
          <Input
            id="email"
            type="email"
            placeholder={t("emailPlaceholder")}
            {...register("email")}
            aria-invalid={errors.email ? "true" : "false"}
          />
          {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>}
        </div>
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            {t("password")}
          </label>
          <PasswordInput
            id="password"
            placeholder={t("passwordPlaceholder")}
            {...register("password")}
            aria-invalid={errors.password ? "true" : "false"}
          />
          {errors.password && <p className="mt-1 text-sm text-red-500">{errors.password.message}</p>}
        </div>

        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
            {t("confirmPassword")}
          </label>
          <PasswordInput
            id="confirmPassword"
            placeholder={t("confirmPasswordPlaceholder")}
            {...register("confirmPassword")}
            aria-invalid={errors.confirmPassword ? "true" : "false"}
          />
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-red-500">{errors.confirmPassword.message}</p>
          )}
        </div>
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            {t("name")}
          </label>
          <Input
            id="name"
            type="text"
            placeholder={t("namePlaceholder")}
            {...register("name")}
            aria-invalid={errors.name ? "true" : "false"}
          />
          {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name.message}</p>}
        </div>

        <div className="flex items-center gap-2">
          <div>
            <Controller
              name="termsAccepted"
              control={control}
              render={({ field }) => (
                <Checkbox
                  id="terms"
                  variant="secondary"
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  aria-invalid={errors.termsAccepted ? "true" : "false"}
                />
              )}
            />
          </div>
          <label htmlFor="terms" className="text-sm text-gray-600">
            {t("termsAgreement")}{" "}
            <Link href="/legal/terms" className="font-semibold text-blue-700">
              {t("termsOfUse")}
            </Link>{" "}
            {t("and")}{" "}
            <Link href="/legal/policy" className="font-semibold text-blue-700">
              {t("privacyPolicy")}
            </Link>{" "}
            {t("acknowledgeTerms")}
          </label>
        </div>
        {errors.termsAccepted && <p className="mt-1 text-sm text-red-500">{errors.termsAccepted.message}</p>}

        <Button onClick={handleSubmit(onSubmit)} variant="primary" size="large" loading={isPending}>
          {t("signupButton")}
        </Button>

        <div className="text-center mt-4">
          {t("alreadyHaveAccount")}{" "}
          <Link href="/login" className="text-mu-base hover:underline">
            {t("login")}
          </Link>
        </div>
      </form>
    </div>
  );
}
