"use client";
import React, { useState, useCallback } from "react";
import dynamic from "next/dynamic";
import { Input } from "../input"; // Assuming Input component is well-defined
import { debounce } from "../../utils/debounce"; // Assuming debounce utility is correct
import { Spinner } from "./spinner";
import { DEFAULT_CONTAINER_STYLE } from "./map-constants";

// Interface for Google Maps search results (MapProps definition should remain unchanged)
export interface MapProps {
  searchResults: google.maps.places.AutocompleteSuggestion[];
  isLoading: boolean;
  onSearchChange: (query: string) => void;
  onSelectLocation: (coords: [number, number], displayName: string, placeId: string) => void;
  selectedCoordinates?: [number, number] | null;
}

// Dynamically load the map components with SSR disabled
const MapWithNoSSR = dynamic(() => import("./map-client-component").then((mod) => mod.MapClientComponent), {
  ssr: false,
  loading: () => (
    <div
      className="flex items-center justify-center w-full bg-gray-100 rounded-md"
      style={{ height: DEFAULT_CONTAINER_STYLE.height }} // Use constant for height
    >
      <Spinner className="h-8 w-8 text-gray-500" />
    </div>
  ),
});

// Minimum characters to trigger search dropdown
const MIN_QUERY_LENGTH_FOR_DROPDOWN = 3;
// Debounce delay for search input
const SEARCH_DEBOUNCE_DELAY = 500;

export function Map({
  searchResults,
  isLoading,
  onSearchChange,
  onSelectLocation,
  selectedCoordinates: initialCoordinates, // Renaming for clarity within the component
}: MapProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  // selectedPlace (placeId) is used to track if the current searchQuery corresponds to a selected item.
  // This helps in scenarios like re-opening dropdown if user continues typing after selecting.
  const [selectedPlaceId, setSelectedPlaceId] = useState<string | null>(null);
  const [mapCenterCoordinates, setMapCenterCoordinates] = useState<[number, number] | null>(
    initialCoordinates || null
  );

  // Update mapCenterCoordinates if initialCoordinates prop changes
  React.useEffect(() => {
    setMapCenterCoordinates(initialCoordinates || null);
  }, [initialCoordinates]);

  const debouncedOnSearchChange = useCallback(
    debounce((query: string) => {
      onSearchChange(query);
    }, SEARCH_DEBOUNCE_DELAY),
    [onSearchChange] // onSearchChange should ideally be memoized by the parent
  );

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    setShowDropdown(query.length >= MIN_QUERY_LENGTH_FOR_DROPDOWN);
    setSelectedPlaceId(null); // Clear selected place if user types new query

    // If no initial coordinates were provided, or if they were cleared,
    // allow the map to react to the search query directly.
    // Otherwise, keep the map centered on `initialCoordinates` or the last `selectedPlace`
    // until a new place is explicitly selected from the dropdown.
    if (!initialCoordinates && !selectedPlaceId) {
      setMapCenterCoordinates(null);
    }
    debouncedOnSearchChange(query);
  };

  const handleSelectPlace = useCallback(
    async (placeSuggestion: google.maps.places.AutocompleteSuggestion) => {
      const placeId = placeSuggestion.placePrediction?.placeId;
      const mainText = placeSuggestion.placePrediction?.mainText?.text;

      if (!placeId || !mainText) {
        console.warn("Selected place suggestion is missing placeId or mainText", placeSuggestion);
        return;
      }

      setSearchQuery(mainText);
      setShowDropdown(false);
      setSelectedPlaceId(placeId);

      if (typeof window.google?.maps?.places?.Place === "undefined") {
        console.error("Google Places API is not available.");
        // Fallback with [0,0] as per existing behavior, though not ideal.
        // Consider if a different fallback or error propagation is better.
        onSelectLocation([0, 0], mainText, placeId);
        return;
      }

      try {
        const place = new window.google.maps.places.Place({ id: placeId });
        await place.fetchFields({ fields: ["location", "displayName", "formattedAddress"] });

        if (place.location) {
          const lat =
            typeof place.location.lat === "function" ? place.location.lat() : Number(place.location.lat);
          const lng =
            typeof place.location.lng === "function" ? place.location.lng() : Number(place.location.lng);

          if (typeof lat === "number" && !isNaN(lat) && typeof lng === "number" && !isNaN(lng)) {
            const coords: [number, number] = [lat, lng];
            setMapCenterCoordinates(coords);
            const displayName = place.displayName || mainText;
            // Use formattedAddress if mainText from suggestion is too short or less descriptive
            setSearchQuery(place.formattedAddress || displayName);
            onSelectLocation(coords, displayName, placeId);
          } else {
            console.error("Invalid coordinates from fetched place details:", { lat, lng }, place);
            onSelectLocation([0, 0], mainText, placeId); // Fallback
          }
        } else {
          console.error("Fetched place details do not include location.", place);
          onSelectLocation([0, 0], mainText, placeId); // Fallback
        }
      } catch (error) {
        console.error("Error fetching place details:", error);
        onSelectLocation([0, 0], mainText, placeId); // Fallback
      }
    },
    [onSelectLocation] // searchQuery and mapCenterCoordinates are managed by setSearchQuery, setMapCenterCoordinates
  );

  return (
    <div className="flex flex-col gap-4 w-full">
      <div className="relative">
        <Input
          type="text"
          placeholder="Search for a location..."
          value={searchQuery}
          onChange={handleSearchInputChange}
          onFocus={() => searchQuery.length >= MIN_QUERY_LENGTH_FOR_DROPDOWN && setShowDropdown(true)}
          // onBlur={() => setTimeout(() => setShowDropdown(false), 150)} // To allow click on dropdown
          className="w-full"
        />

        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <Spinner className="h-5 w-5 text-gray-500" /> {/* Standard size for input spinner */}
          </div>
        )}

        {showDropdown && searchResults && searchResults.length > 0 && (
          <div
            className="absolute z-[9999] mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-y-auto"
            // onMouseDown={(e) => e.preventDefault()} // Prevents onBlur from firing before click
          >
            {searchResults.map((result) => (
              <div
                key={result.placePrediction?.placeId || Math.random().toString()} // Ensure key is always unique
                role="button"
                tabIndex={0}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={() => handleSelectPlace(result)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") handleSelectPlace(result);
                }}>
                {result.placePrediction ? (
                  <>
                    {" "}
                    {/* Use Fragment shorthand */}
                    <div className="font-medium">{result.placePrediction.mainText?.text}</div>
                    <div className="text-sm text-gray-500">{result.placePrediction.secondaryText?.text}</div>
                  </>
                ) : (
                  <div className="text-sm text-gray-500">Invalid suggestion</div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Map Display Area */}
      <div className="relative">
        <MapWithNoSSR
          searchQuery={searchQuery} // Pass current search query
          selectedCoordinates={mapCenterCoordinates} // Pass selected/initial coordinates
          height={DEFAULT_CONTAINER_STYLE.height} // Consistent height
        />
        {/* The "View only mode" overlay. Its purpose should be clear in the context of the application.
            If the map *is* interactive, this text might be misleading.
            If it's meant to disable interaction, MapClientComponent props should reflect that.
            Keeping as is per original code for now. */}
        <div className="absolute inset-0 bg-transparent pointer-events-none border-2 border-gray-300 rounded-md flex items-center justify-center">
          <div className="bg-white/80 px-3 py-1 text-sm text-gray-700 rounded shadow">View only mode</div>
        </div>
      </div>
    </div>
  );
}

export default Map;
