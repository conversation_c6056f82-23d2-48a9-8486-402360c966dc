import { useMutation } from "@tanstack/react-query";
import { z } from "zod";

import { useToast } from "@meeeetup/ui/toaster";

import { answerCreateBodySchema } from "@/schema/form/create";
import { applyEventApi } from "../../../_api"; // Path relative to this hook file

export function useEventRegistration(eventId: string | undefined) {
  const { toast } = useToast();

  const { mutate: applyEvent, isPending } = useMutation({
    mutationFn: async (data: {
      answers: z.infer<typeof answerCreateBodySchema>[];
      ticketTypeId: string;
      agreeLegal: boolean;
    }) => {
      if (!eventId) throw new Error("Event ID is required for registration.");
      return applyEventApi(eventId, {
        answers: data.answers,
        ticketTypeId: data.ticketTypeId,
        agreeLegal: data.agreeLegal,
      });
    },
    onSuccess: (data) => {
      if (data.success && data.data.url) {
        window.location.href = data.data.url;
      } else if (!data.success) {
        toast({
          title: "Error",
          description: data.error.details,
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      console.error("Failed to apply for event:", error);
      toast({
        title: "Error",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    },
  });

  return {
    applyEvent,
    isPending,
  };
}
