"use client";

import { useState } from "react";
import { useA<PERSON><PERSON><PERSON><PERSON>, useSocialLogin } from "@/hooks/use-auth-handlers";
import { useTranslations } from "next-intl";

interface UseGoogleLoginProps {
  onLoginSuccess?: (nextUrl?: string) => void;
  onLoginError?: (error?: string) => void;
  defaultLoginError?: string;
}

export function useGoogleLogin({
  onLoginSuccess,
  onLoginError,
  defaultLoginError,
}: UseGoogleLoginProps = {}) {
  const [googleLoading, setGoogleLoading] = useState(false);
  const { handleGoogleLogin: handleSocialGoogleLogin } = useSocialLogin();
  const { handleLoginError } = useAuthHandlers();
  const t = useTranslations("LoginPage"); // Or a more generic namespace if preferred

  const handleGoogleLogin = async (loginType: "organizer" | "participant", nextUrl?: string) => {
    setGoogleLoading(true);
    try {
      await handleSocialGoogleLogin(loginType, nextUrl);
      // Assuming handleSocialGoogleLogin will internally call the success/error handlers from useAuthHandler
      // or redirect appropriately. If direct success/error feedback is needed here, it would be more complex.
      if (onLoginSuccess) {
        // Potentially, useAuthHandler's success could be passed through or re-invoked.
        // For now, let's assume social login handles its own success navigation/toast.
        // onLoginSuccess(nextUrl);
      }
    } catch (error: any) {
      console.error("Error handling Google login:", error);
      const errorMessage = error?.message || defaultLoginError || t("googleLoginFailed");
      if (onLoginError) {
        onLoginError(errorMessage);
      } else {
        handleLoginError(errorMessage);
      }
    } finally {
      setGoogleLoading(false);
    }
  };

  return {
    googleLoading,
    handleGoogleLogin,
    setGoogleLoading, // Exposing this if manual control over loading state is needed
  };
}
