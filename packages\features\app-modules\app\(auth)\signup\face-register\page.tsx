"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2, Check, AlertCircle } from "lucide-react";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";

import { FacialReceptionCapture } from "@meeeetup/ui/facial-reception-capture";
import { Button } from "@meeeetup/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@meeeetup/ui/card";

import { base64ToFile, registerFaceApi } from "./_api";

function FaceRegister() {
  const router = useRouter();
  const { update: updateSession } = useSession();
  const [image, setImage] = useState<string>("");
  const [status, setStatus] = useState<"idle" | "confirming" | "loading" | "success" | "error">("idle");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [nextUrl, setNextUrl] = useState<string | null>(null);
  const t = useTranslations("FaceRegistration");

  useEffect(() => {
    // Get the next parameter from the URL
    const searchParams = new URLSearchParams(window.location.search);
    const next = searchParams.get("next");
    if (next) {
      setNextUrl(next);
    }
  }, []);

  const handleCapture = (capturedImage: string) => {
    setImage(capturedImage);
    setStatus("confirming");
  };

  const handleConfirm = async () => {
    setStatus("loading");

    try {
      const file = await base64ToFile(image, "face-image.jpg");
      const data = await registerFaceApi(file);

      if (!data.success) {
        throw new Error(data.message || "Failed to register face");
      }

      setStatus("success");

      // Update the session to reflect the face registration
      await updateSession();

      setTimeout(() => {
        router.push(nextUrl || "/participant/dashboard");
      }, 500);
    } catch (error) {
      setStatus("error");
      setErrorMessage(
        error instanceof Error ? error.message : "An error occurred while registering your face"
      );
    }
  };

  const handleRetake = () => {
    setImage("");
    setStatus("idle");
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen max-h-screen p-4 overflow-hidden">
      <div className="w-full max-w-2xl">
        <button
          onClick={() => router.back()}
          className="mb-2 px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-100 block mx-auto">
          {t("backButton")}
        </button>

        <Card className="h-auto">
          <CardHeader className="py-3">
            <CardTitle className="text-center text-lg sm:text-xl">{t("title")}</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="w-full">
              {status === "idle" && (
                <div className="flex flex-col">
                  <p className="mb-2 px-4 text-center text-sm text-gray-600">{t("captureInstruction")}</p>
                  <div className="relative z-10">
                    <FacialReceptionCapture
                      autoCapture={false}
                      onCapture={handleCapture}
                      className="max-h-[calc(100vh-220px)]"
                    />
                  </div>
                </div>
              )}

              {status === "confirming" && (
                <div className="flex flex-col items-center justify-center py-4 px-4">
                  <p className="mb-2 text-center text-sm text-gray-600">{t("confirmInstruction")}</p>
                  <div
                    className="w-full max-w-md mx-auto bg-gray-50 rounded-xl overflow-hidden border border-gray-100 shadow-inner mb-4 flex items-center justify-center"
                    style={{ height: "min(240px, calc(100vh - 320px))" }}>
                    <img
                      src={image}
                      alt={t("capturedImageAlt")}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                  <div className="flex gap-2 w-full max-w-md mx-auto">
                    <Button
                      onClick={handleRetake}
                      className="w-1/2 text-xs sm:text-sm py-3 sm:py-[25px]"
                      variant="outline"
                      size="large">
                      {t("retakeButton")}
                    </Button>
                    <Button
                      onClick={handleConfirm}
                      className="w-1/2 text-xs sm:text-sm py-3 sm:py-[25px]"
                      variant="primary"
                      size="large">
                      {t("confirmButton")}
                    </Button>
                  </div>
                </div>
              )}

              {status === "loading" && (
                <div className="flex flex-col items-center justify-center py-12">
                  <Loader2 className="w-10 h-10 text-blue-500 animate-spin" />
                  <p className="mt-3 text-center text-sm text-gray-600">{t("registeringMessage")}</p>
                </div>
              )}

              {status === "success" && (
                <div className="flex flex-col items-center justify-center py-12">
                  <div className="p-2 rounded-full bg-green-100">
                    <Check className="w-8 h-8 text-green-500" />
                  </div>
                  <p className="mt-3 text-center text-sm text-gray-600">{t("successMessage")}</p>
                  <p className="text-center text-xs text-gray-500">{t("redirectingMessage")}</p>
                </div>
              )}

              {status === "error" && (
                <div className="flex flex-col items-center justify-center py-8 px-4">
                  <div className="p-2 rounded-full bg-red-100">
                    <AlertCircle className="w-8 h-8 text-red-500" />
                  </div>
                  <p className="mt-3 text-center text-sm text-gray-600">{t("errorMessage")}</p>
                  <p className="text-center text-xs text-red-500">{errorMessage}</p>
                </div>
              )}
            </div>
          </CardContent>

          {status === "error" && (
            <CardFooter className="py-3">
              <Button onClick={() => setStatus("idle")} className="w-full py-2 text-sm" variant="outline">
                {t("tryAgainButton")}
              </Button>
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  );
}

export default FaceRegister;
