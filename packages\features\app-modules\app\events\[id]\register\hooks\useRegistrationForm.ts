import { useEffect, useMemo } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";

import { BASIC_QUESTIONS } from "@/constants/form-questions";
import { answerCreateBodySchema } from "@/schema/form/create";
import { FormSchemaType, createRegisterEventFormSchema, createQuestionSchema } from "../_components/types";
import { Question, QuestionType as FormQuestionType } from "../_components/question-info-form";
import { EventWithQuestionsApiResponseData } from "../../../_api";

interface UseRegistrationFormProps {
  event: EventWithQuestionsApiResponseData | null;
  questions: Question[];
  applyEvent: (data: {
    answers: z.infer<typeof answerCreateBodySchema>[];
    ticketTypeId: string;
    agreeLegal: boolean;
  }) => void;
}

export function useRegistrationForm({ event, questions, applyEvent }: UseRegistrationFormProps) {
  const t = useTranslations("EventRegistration");
  const session = useSession();
  const participantData = session.data?.user.participantProfile;
  const education = participantData?.education;
  const workExperience = participantData?.workExperience;

  const [basicQuestions, additionalQuestions] = useMemo(
    () => [questions.filter((q) => q.isBasic), questions.filter((q) => !q.isBasic)],
    [JSON.stringify(questions)]
  );

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(
      (() => {
        const questionRelatedSchema = createQuestionSchema(questions, t);
        const registerEventFormSchema = createRegisterEventFormSchema(t);
        if (event?.form?.privacyPolicy || event?.form?.termsOfUse) {
          const legalRequiredSchema = z.object({
            ticketTypeId: registerEventFormSchema.shape.ticketTypeId,
            agreeLegal: z.boolean().refine((val) => val === true, {
              message: t("agreeLegalError"),
            }),
          });
          return questionRelatedSchema.merge(legalRequiredSchema);
        } else {
          const legalOptionalSchema = z.object({
            ticketTypeId: registerEventFormSchema.shape.ticketTypeId,
            agreeLegal: registerEventFormSchema.shape.agreeLegal,
          });
          return questionRelatedSchema.merge(legalOptionalSchema);
        }
      })()
    ),
    defaultValues: {
      ticketTypeId: "",
      agreeLegal: false,
    },
  });

  useEffect(() => {
    if (participantData && basicQuestions.length > 0) {
      const defaultValues: { [key: string]: any } = {};
      basicQuestions.forEach((question) => {
        let value: any;
        switch (question.questionText) {
          case BASIC_QUESTIONS.FULL_NAME:
            value = participantData.name || undefined;
            break;
          case BASIC_QUESTIONS.EMAIL_ADDRESS:
            value = participantData.email || undefined;
            break;
          case BASIC_QUESTIONS.PHONE_NUMBER:
            value = participantData.phoneNumber || undefined;
            break;
          case BASIC_QUESTIONS.BIO:
            value = participantData.bio || undefined;
            break;
          case BASIC_QUESTIONS.GENDER:
            value = participantData.gender || undefined;
            break;
          case BASIC_QUESTIONS.SNS:
            if (participantData.socialLinks) {
              try {
                const links =
                  typeof participantData.socialLinks === "string"
                    ? JSON.parse(participantData.socialLinks)
                    : participantData.socialLinks;
                value = JSON.stringify(links);
              } catch (e) {
                console.error("Error formatting social links for autofill:", e);
                value = "[]";
              }
            }
            break;
          case BASIC_QUESTIONS.WORK_EXPERIENCE:
            if (workExperience && Array.isArray(workExperience) && workExperience.length > 0) {
              const structuredData = workExperience.map((exp) => ({
                id: exp.id || `work-exp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                position: exp.position || "",
                startDate: exp.startDate || new Date().toISOString(),
                endDate: exp.endDate,
                company: {
                  name: exp.company?.name || "",
                  websiteUrl: exp.company?.websiteUrl || "",
                },
              }));
              value = JSON.stringify(structuredData);
            }
            break;
          case BASIC_QUESTIONS.EDUCATION:
            if (education && Array.isArray(education) && education.length > 0) {
              const structuredData = education.map((edu) => ({
                id: edu.id || `edu-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                university: edu.university || "",
                degree: edu.degree || "",
                major: edu.major || "",
                startDate: edu.startDate || new Date().toISOString(),
                endDate: edu.endDate,
              }));
              value = JSON.stringify(structuredData);
            }
            break;
        }
        if (value !== undefined) {
          defaultValues[question.id] = value;
        }
      });

      form.reset({
        ...form.getValues(),
        ...defaultValues,
      });
    }
  }, [participantData, basicQuestions, workExperience, education, form.reset, form.getValues]);

  const updateAnswer = (questionId: string, answer: string | string[]) => {
    form.setValue(questionId, answer, { shouldValidate: true });
  };

  const onSubmitHandler = form.handleSubmit((data) => {
    const answers: z.infer<typeof answerCreateBodySchema>[] = [];
    Object.entries(data).forEach(([key, value]) => {
      const question = questions.find((q) => q.id === key);
      if (!question) return;

      if (value !== undefined && value !== null && (typeof value === "string" ? value.trim() !== "" : true)) {
        let formattedAnswer = value;

        if (
          question.questionText === BASIC_QUESTIONS.WORK_EXPERIENCE ||
          question.questionText === BASIC_QUESTIONS.EDUCATION
        ) {
          if (typeof value === "string" && value.startsWith("[")) {
            try {
              JSON.parse(value);
              formattedAnswer = value;
            } catch (e) {
              formattedAnswer = value;
            }
          } else {
            formattedAnswer = typeof value === "string" ? value : JSON.stringify(value);
          }
        } else if (question.questionType === FormQuestionType.CHECKBOX) {
          if (Array.isArray(value)) {
            formattedAnswer = JSON.stringify(value);
          } else if (typeof value === "string") {
            try {
              JSON.parse(value);
              formattedAnswer = value;
            } catch (e) {
              formattedAnswer = JSON.stringify([value]);
            }
          }
        }

        answers.push({
          questionId: key,
          answer: typeof formattedAnswer === "string" ? formattedAnswer : JSON.stringify(formattedAnswer),
        });
      }
    });

    applyEvent({
      answers,
      ticketTypeId: data.ticketTypeId,
      agreeLegal: !!data.agreeLegal,
    });
  });

  return {
    form,
    basicQuestions,
    additionalQuestions,
    updateAnswer,
    onSubmitHandler,
    Controller,
  };
}
