import { NextRequest } from "next/server";
import { CheckInOutActionType } from "@prisma/client";

import { prisma } from "@meeeetup/prisma";

import { responseJson } from "@/lib/response";
import { errors } from "@/lib/errors";

// and only accessible from your service
const SECRET = "Sec_2qpgu40006calab6cgJ1tt";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    if (!SECRET || body.secret !== SECRET) {
      throw errors.notAuthorized();
    }

    const now = new Date();
    const todayStart = new Date(
      Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0, 0)
    );
    const todayEnd = new Date(
      Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999)
    );

    const checkedInParticipants = await prisma.eventParticipant.findMany({
      where: {
        checkInTime: {
          gte: todayStart,
          lte: todayEnd,
        },
        status: "CHECKED_IN",
        checkInOutRecord: {
          some: {
            actionTime: {
              gte: todayStart,
              lte: todayEnd,
            },
            actionType: CheckInOutActionType.CHECK_IN,
          },
        },
      },
      include: {
        checkInOutRecord: {
          where: {
            actionTime: {
              gte: todayStart,
              lte: todayEnd,
            },
          },
          orderBy: {
            actionTime: "desc",
          },
        },
      },
    });

    const checkoutTime = new Date();
    const results = await prisma.$transaction(
      checkedInParticipants.map((participant) => {
        return prisma.checkInOutRecord.create({
          data: {
            participantId: participant.id,
            actionType: CheckInOutActionType.CHECK_OUT,
            actionTime: checkoutTime,
            method: "MANUAL",
          },
        });
      })
    );

    // Update all participants' status
    if (checkedInParticipants.length > 0) {
      await prisma.eventParticipant.updateMany({
        where: {
          id: {
            in: checkedInParticipants.map((p) => p.id),
          },
        },
        data: {
          status: "CHECKED_OUT",
        },
      });
    }

    return responseJson.success({
      data: {
        processedCount: results.length,
        timestamp: checkoutTime,
      },
      message: `Auto-checkout completed for ${results.length} participants`,
    });
  } catch (error) {
    return responseJson.error({
      error: error,
      message: "Nightly auto-checkout failed",
    });
  }
}
