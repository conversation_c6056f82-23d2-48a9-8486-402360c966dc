generator client {
  provider = "prisma-client-js"
  // output   = "./generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token") @db.VarChar(512)
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id                  String              @id @default(cuid())
  stripeCustomerId    String?             @map("stripe_customer_id") @db.VarChar(255)
  name                String?             @db.VarChar(100)
  email               String              @unique @db.VarChar(255)
  password            String?             @db.VarChar(255)
  phoneNumber         String?             @unique @map("phone_number") @db.VarChar(20)
  phoneNumberVerified Boolean?            @default(false) @map("phone_number_verified")
  emailVerified       Boolean?            @default(false) @map("email_verified")
  image               String?             @db.VarChar(512)
  accounts            Account[]
  sessions            Session[]
  roles               Role[]              @relation("UserRoles")
  participantProfile  ParticipantProfile?
  hostProfile         HostProfile?
  activeRoleId        String?             @map("active_role_id")
  activeRole          Role?               @relation("ActiveUserRole", fields: [activeRoleId], references: [id], onDelete: SetNull)
  eventParticipant    EventParticipant[]
  createdAt           DateTime            @default(now()) @map("created_at")
  updatedAt           DateTime            @updatedAt @map("updated_at")
  givenFavorites      Favorite[]          @relation("GivenFavorites")
  receivedFavorites   Favorite[]          @relation("ReceivedFavorites")
  // tickets             Ticket[]

  @@map("users")
}

model VerificationToken {
  identifier String                @db.VarChar(255)
  token      String                @unique @db.VarChar(512)
  type       VerificationTokenType
  expires    DateTime
  createdAt  DateTime              @default(now()) @map("created_at")

  @@unique([identifier, token, type])
  @@map("verification_tokens")
}

model Role {
  id          String   @id @default(cuid())
  name        RoleName @unique
  users       User[]   @relation("UserRoles")
  activeUsers User[]   @relation("ActiveUserRole")
  createdAt   DateTime @default(now()) @map("created_at")

  @@map("roles")
}

model ParticipantProfile {
  userId         String           @id @map("user_id")
  imgUrl         String?          @db.VarChar(255)
  name           String?          @db.VarChar(100)
  email          String?          @db.VarChar(255)
  phoneNumber    String?          @map("phone_number") @db.VarChar
  dob            DateTime?        @db.Date
  gender         String?          @db.VarChar(255)
  bio            String?          @db.Text
  socialLinks    Json?            @map("social_links")
  education      Education[]
  workExperience WorkExperience[]
  createdAt      DateTime         @default(now()) @map("created_at")
  updatedAt      DateTime         @updatedAt @map("updated_at")

  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  FaceAuthData FaceAuthData[]

  @@map("participant_profiles")
}

model WorkExperience {
  id                       String              @id @default(cuid())
  userId                   String              @map("user_id")
  position                 String              @db.VarChar(255)
  startDate                DateTime            @map("start_date")
  endDate                  DateTime?           @map("end_date")
  createdAt                DateTime            @default(now()) @map("created_at")
  updatedAt                DateTime            @updatedAt @map("updated_at")
  participantProfile       ParticipantProfile? @relation(fields: [participantProfileUserId], references: [userId], onDelete: Cascade)
  participantProfileUserId String?
  company                  Company             @relation(fields: [companyId], references: [id])
  companyId                String

  @@map("work_experiences")
}

model Education {
  id                       String              @id @default(cuid())
  userId                   String              @map("user_id")
  university               String              @db.VarChar(255)
  major                    String              @db.VarChar(255)
  degree                   String?             @db.VarChar(255)
  startDate                DateTime?           @map("start_date")
  endDate                  DateTime?           @map("end_date")
  createdAt                DateTime            @default(now()) @map("created_at")
  updatedAt                DateTime            @updatedAt @map("updated_at")
  participantProfile       ParticipantProfile? @relation(fields: [participantProfileUserId], references: [userId], onDelete: Cascade)
  participantProfileUserId String?
  hostProfile              HostProfile?        @relation(fields: [hostProfileUserId], references: [userId], onDelete: Cascade)
  hostProfileUserId        String?

  @@map("educations")
}

model FaceAuthData {
  id               String   @id @default(cuid())
  faceId           String   @map("face_id")
  imageId          String   @map("image_id")
  externalImageId  String   @map("external_image_id")
  s3Url            String   @map("s3_url")
  faceRegisteredAt DateTime @map("face_registered_at")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  participant   ParticipantProfile @relation(fields: [participantId], references: [userId], onDelete: Cascade)
  participantId String

  @@map("face_auth_data")
}

model HostProfile {
  userId             String      @id @map("user_id")
  imgUrl             String?     @db.VarChar(255)
  bio                String?     @db.Text
  gender             String?     @db.VarChar(255)
  dob                DateTime?   @db.Date()
  socialLinks        Json?       @map("social_links")
  name               String?     @db.VarChar(255)
  email              String?     @db.VarChar(255)
  phoneNumber        String?     @map("phone_number") @db.VarChar(20)
  rolePosition       String?     @map("role_position") @db.VarChar(255)
  education          Education[]
  verificationStatus Boolean?    @map("verification_status")
  createdAt          DateTime    @default(now()) @map("created_at")
  updatedAt          DateTime    @updatedAt @map("updated_at")

  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  events            Event[]
  company           Company?           @relation(fields: [companyId], references: [id], onDelete: SetNull)
  companyId         String?            @unique @map("company_id")
  payoutInformation PayoutInformation?
  admins            Admin[]

  @@map("host_profiles")
}

model PayoutInformation {
  id               String   @id @default(cuid())
  hostId           String   @unique
  accountOwnerName String   @map("account_owner_name") @db.VarChar(255)
  bankCode         String   @map("bank_code") @db.VarChar(50)
  branchCode       String   @map("branch_code") @db.VarChar(50)
  accountType      String   @map("account_type") @db.VarChar(50)
  accountNumber    String   @map("account_number") @db.VarChar(50)
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  host HostProfile @relation(fields: [hostId], references: [userId], onDelete: Cascade)

  @@map("payout_information")
}

model Company {
  id                 String   @id @default(cuid())
  name               String   @db.VarChar(255)
  description        String?  @db.Text
  email              String?  @db.VarChar(255)
  phoneNumber        String?  @db.VarChar(20)
  websiteUrl         String?  @db.VarChar(255)
  socialLinks        Json?    @map("social_links")
  verificationStatus Boolean? @map("verification_status")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  hostProfile    HostProfile?
  WorkExperience WorkExperience[]

  @@map("companies")
}

model Event {
  id             String      @id @default(cuid())
  hostId         String
  name           String      @db.VarChar(255)
  description    String?     @db.Text
  bannerImageUrl String?     @map("banner_image_url") @db.VarChar(512)
  startDatetime  DateTime    @map("start_datetime")
  endDatetime    DateTime    @map("end_datetime")
  timezone       String      @default("UTC") @db.VarChar(50)
  location       Json?
  tags           String[]
  status         EventStatus @default(DRAFT)
  createdAt      DateTime    @default(now()) @map("created_at")
  updatedAt      DateTime    @updatedAt @map("updated_at")

  host             HostProfile        @relation(fields: [hostId], references: [userId], onDelete: Cascade)
  ticketTypes      TicketType[]
  eventParticipant EventParticipant[]
  ticket           Ticket[]
  admins           Admin[]

  form     Form?
  Favorite Favorite[]

  @@map("events")
}

model Admin {
  id        String   @id @default(cuid())
  eventId   String   @map("event_id")
  hostId    String   @map("host_id")
  addedBy   String   @map("added_by")
  active    Boolean  @default(true)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  event Event       @relation(fields: [eventId], references: [id])
  host  HostProfile @relation(fields: [hostId], references: [userId])

  @@unique([eventId, hostId], name: "eventId_hostId_unique")
  @@map("admins")
}

model EventParticipant {
  id            String            @id @default(cuid())
  eventId       String
  userId        String?           @map("user_id")
  guestName     String?           @map("guest_name") @db.VarChar(100)
  guestEmail    String?           @map("guest_email") @db.VarChar(255)
  ticketId      String?           @map("ticket_id")
  status        ParticipantStatus @default(PENDING) // New status
  checkInTime   DateTime?         @map("check_in_time")
  checkInMethod CheckInMethod?    @map("check_in_method")
  createdAt     DateTime          @default(now()) @map("created_at")
  updatedAt     DateTime          @updatedAt @map("updated_at")

  event          Event            @relation(fields: [eventId], references: [id], onDelete: Cascade)
  user           User?            @relation(fields: [userId], references: [id], onDelete: Cascade)
  ticket         Ticket?          @relation(fields: [ticketId], references: [id], onDelete: SetNull)
  formSubmission FormSubmission[]

  checkInOutRecord CheckInOutRecord[]

  notes Note[]

  @@unique([eventId, userId], name: "event_user_unique")
  @@unique([eventId, guestEmail], name: "event_guest_email_unique")
  @@map("event_participants")
}

model Note {
  id                 String           @id @default(cuid())
  EventParticipant   EventParticipant @relation(fields: [eventParticipantId], references: [id], onDelete: Cascade)
  eventParticipantId String
  active             Boolean          @default(true)
  noteText           String?
  type               NoteType         @default(HOST_TO_PARTICIPANT)
  writtenBy          String // event participant id [not user id/because of guest] / host id

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("notes")
}

model CheckInOutRecord {
  id            String               @id @default(cuid())
  participantId String               @map("participant_id")
  actionType    CheckInOutActionType
  actionTime    DateTime             @default(now()) @map("action_time")
  method        CheckInMethod?       @map("method")
  createdAt     DateTime             @default(now()) @map("created_at")
  updatedAt     DateTime             @updatedAt @map("updated_at")

  participant EventParticipant @relation(fields: [participantId], references: [id], onDelete: Cascade)

  @@map("check_in_out_records")
}

model Favorite {
  id         String   @id @default(cuid())
  giverId    String   @map("giver_id")
  receiverId String   @map("receiver_id")
  eventId    String?  @map("event_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  giver    User   @relation("GivenFavorites", fields: [giverId], references: [id], onDelete: Cascade)
  receiver User   @relation("ReceivedFavorites", fields: [receiverId], references: [id], onDelete: Cascade)
  event    Event? @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@unique([giverId, receiverId], name: "unique_favorite")
  @@map("favorites")
}

model Ticket {
  id           String       @id @default(cuid())
  ticketTypeId String
  userId       String
  eventId      String
  paymentId    String       @unique
  status       TicketStatus @default(RESERVED)
  active       Boolean      @default(true)
  qrCode       String?      @unique @map("qr_code") @db.VarChar(512)
  checkedInAt  DateTime?    @map("checked_in_at")
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @updatedAt @map("updated_at")

  ticketType       TicketType         @relation(fields: [ticketTypeId], references: [id])
  event            Event              @relation(fields: [eventId], references: [id])
  payment          Payment?           @relation(fields: [paymentId], references: [id])
  eventParticipant EventParticipant[]
  // User             User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("tickets")
}

model TicketType {
  id              String   @id @default(cuid())
  eventId         String
  name            String   @db.VarChar(255)
  description     String?  @db.Text
  price           Decimal  @db.Decimal(10, 2)
  active          Boolean  @default(true)
  stripeProductId String?  @map("stripe_product_id") @db.VarChar(255)
  stripePriceId   String?  @map("stripe_price_id") @db.VarChar(255)
  quantity        Int
  availableFrom   DateTime @map("available_from")
  availableUntil  DateTime @map("available_until")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  event   Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  tickets Ticket[]

  @@map("ticket_types")
}

model Payment {
  id            String        @id @default(cuid())
  amount        Decimal       @db.Decimal(10, 2)
  currency      String        @default("USD") @db.VarChar(3)
  status        PaymentStatus
  paymentMethod String?       @map("payment_method") @db.VarChar(50)
  transactionId String?       @unique @map("transaction_id") @db.VarChar(255)
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")

  tickets Ticket[]

  @@map("payments")
}

model Form {
  id            String     @id @default(cuid())
  eventId       String     @unique @map("event_id")
  questions     Question[]
  name          String     @db.VarChar(255)
  description   String?    @db.Text
  termsOfUse    String?    @map("terms_of_use") @db.Text
  privacyPolicy String?    @map("privacy_policy") @db.Text
  createdAt     DateTime   @default(now()) @map("created_at")
  updatedAt     DateTime   @updatedAt @map("updated_at")

  formSubmissions FormSubmission[]
  event           Event            @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@map("forms")
}

model Question {
  id           String       @id @default(cuid())
  formId       String       @map("form_id")
  questionText String       @map("question_text") @db.Text
  questionType QuestionType @default(TEXT) @map("question_type")
  isBasic      Boolean      @default(false) @map("is_basic")
  isPrivate    Boolean      @default(false) @map("is_private")
  options      Json?
  isRequired   Boolean      @default(true) @map("is_required")
  order        Int?
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @updatedAt @map("updated_at")
  formAnswers  FormAnswer[]

  form Form @relation(fields: [formId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@map("questions")
}

model FormSubmission {
  id            String   @id @default(cuid())
  formId        String   @map("form_id")
  participantId String   @map("participant_id")
  agreeLegal    Boolean  @default(true) @map("agree_legal")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  answers          FormAnswer[]
  form             Form?             @relation(fields: [formId], references: [id], onDelete: Cascade)
  eventParticipant EventParticipant? @relation(fields: [participantId], references: [id], onDelete: Cascade)

  @@map("form_submissions")
}

model FormAnswer {
  id               String   @id @default(cuid())
  formSubmissionId String   @map("form_submission_id")
  questionId       String   @map("question_id")
  answer           Json?
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  formSubmission FormSubmission @relation(fields: [formSubmissionId], references: [id], onDelete: Cascade)
  question       Question       @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@map("form_answers")
}

model PolicyAndTerms {
  id          String @id @default(cuid())
  type        String @default("policy")
  name        String
  description String

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("policy_and_terms")
}

enum QuestionType {
  TEXT
  NUMBER
  RADIO
  CHECKBOX
  DROPDOWN
}

enum VerificationTokenType {
  SIGN_UP
  LOGIN
  RESET_PASSWORD
}

enum RoleName {
  participant
  host
  admin
}

enum PlanType {
  BASIC
  PRO
  ENTERPRISE
}

enum EventStatus {
  DRAFT
  PUBLISHED
  CANCELLED
  COMPLETED
  LIMITED_PUBLISHED
}

enum ParticipantStatus {
  PENDING
  APPROVED
  CHECKED_IN
  CHECKED_OUT
  CANCELLED
}

enum CheckInMethod {
  QR_CODE
  MANUAL
  FACE_RECOGNITION
}

enum TicketStatus {
  RESERVED
  PAID
  CANCELLED
  USED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum CheckInOutActionType {
  CHECK_IN
  CHECK_OUT
}

enum NoteType {
  PARTICIPANT_TO_PARTICIPANT
  HOST_TO_PARTICIPANT
}
