"use client";

import { <PERSON><PERSON> } from "@meeeetup/ui/button";
import { Check<PERSON><PERSON>cle2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Left, <PERSON><PERSON><PERSON> } from "lucide-react";
import { useTranslations } from "next-intl";
import { useFacialReceptionContext } from "../../context/FacialReceptionContext";

export function PermissionView() {
  const { dispatch, handleNavigateBack } = useFacialReceptionContext();
  const t = useTranslations("FacialReception.PermissionView");

  const handleContinue = () => {
    // Reset the state so that the camera view starts fresh.
    dispatch({ type: "RESET" });
  };

  const handleFinish = () => {
    // Simply navigate back to the previous page (dashboard overview).
    handleNavigateBack();
  };

  return (
    <div className="w-full max-w-lg">
      <div className="bg-white rounded-3xl shadow-lg border border-slate-200/60 p-8 sm:p-10 text-center relative overflow-hidden">
        {/* Decorative background elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-mu-base/5 to-blue-500/5 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-green-500/5 to-mu-accent/5 rounded-full translate-y-12 -translate-x-12"></div>

        {/* Success icon with animation */}
        <div className="relative mb-6">
          <div className="w-20 h-20 bg-gradient-to-br from-green-100 to-green-50 rounded-full flex items-center justify-center mx-auto mb-4 relative">
            <div className="absolute inset-0 bg-green-500/10 rounded-full animate-ping"></div>
            <CheckCircle2 className="w-10 h-10 text-green-600 relative z-10" />
          </div>
          <div className="flex items-center justify-center gap-1 text-green-600">
            <Sparkles className="w-4 h-4 animate-pulse" />
            <span className="text-sm font-medium">{t("processed")}</span>
            <Sparkles className="w-4 h-4 animate-pulse" />
          </div>
        </div>

        {/* Main content */}
        <div className="relative z-10 mb-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-4 leading-tight">{t("title")}</h2>
          <p className="text-slate-600 text-base leading-relaxed max-w-sm mx-auto">{t("description")}</p>
        </div>

        {/* Action buttons with improved styling */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center relative z-10">
          <Button
            onClick={handleContinue}
            variant="primary"
            size="large"
            className="flex-1 sm:flex-none sm:min-w-[160px] py-4 text-base font-semibold flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5">
            <RotateCcw className="w-5 h-5" />
            {t("continueButton")}
          </Button>
          <Button
            variant="secondary"
            onClick={handleFinish}
            size="large"
            className="flex-1 sm:flex-none sm:min-w-[160px] py-4 text-base font-semibold flex items-center justify-center gap-2 transition-all duration-300 hover:-translate-y-0.5">
            <ArrowLeft className="w-5 h-5" />
            {t("finishButton")}
          </Button>
        </div>

        {/* Subtle bottom accent */}
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-mu-base to-mu-accent rounded-full"></div>
      </div>
    </div>
  );
}
