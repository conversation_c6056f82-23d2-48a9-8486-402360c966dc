import { useState, useEffect, KeyboardEvent } from "react";
import { X, ListFilter, Type } from "lucide-react";

import { But<PERSON> } from "@meeeetup/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@meeeetup/ui/dialog";
import { Input } from "@meeeetup/ui/input";
import { Switch } from "@meeeetup/ui/switch";
import { Badge } from "@meeeetup/ui";

import { type QuestionWithId, type QuestionType } from "./form-questions";

// Define Option type locally if not imported, or import if available globally
interface Option {
  id: string;
  text: string;
}

interface AddEditQuestionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (question: QuestionWithId) => void;
  initialData?: QuestionWithId | null;
  questionType?: QuestionType; // To determine if options are needed
}

// A simplified QuestionType to manage internal state for selection type
type LocalQuestionType = "Single" | "Multiple" | "Text";

const mapQuestionTypeToLocal = (type?: QuestionType): LocalQuestionType => {
  if (type === "Single Choose Question") return "Single";
  if (type === "Multiple Choose Question") return "Multiple";
  if (type === "Text") return "Text";
  return "Single"; // Default
};

const mapLocalToQuestionType = (localType: LocalQuestionType): QuestionType => {
  if (localType === "Single") return "Single Choose Question";
  if (localType === "Multiple") return "Multiple Choose Question";
  if (localType === "Text") return "Text";
  return "Single Choose Question"; // Default, though should always be one of the options
};

export function AddEditQuestionDialog({
  isOpen,
  onClose,
  onSave,
  initialData,
  questionType: initialQuestionType = "Single Choose Question", // Default from props
}: AddEditQuestionDialogProps) {
  const t = "host.dashboard.events.applicationForm";
  const [questionText, setQuestionText] = useState("");
  const [options, setOptions] = useState<Option[]>([]); // Changed from string[] to Option[]
  const [currentOptionText, setCurrentOptionText] = useState(""); // Renamed for clarity
  const [selectionType, setSelectionType] = useState<LocalQuestionType>(
    mapQuestionTypeToLocal(initialQuestionType)
  );
  const [isRequired, setIsRequired] = useState(false);
  const [isPrivate, setIsPrivate] = useState(false); // Assuming this might be needed later

  useEffect(() => {
    if (initialData) {
      setQuestionText(initialData.question);
      setOptions(initialData.options || []); // initialData.options is Option[]
      setSelectionType(mapQuestionTypeToLocal(initialData.type));
      setIsRequired(initialData.isRequired);
      setIsPrivate(initialData.isPrivate);
    } else {
      // Reset for new question
      setQuestionText("");
      setOptions([]);
      setCurrentOptionText(""); // Renamed
      setSelectionType(mapQuestionTypeToLocal(initialQuestionType)); // Use prop for new q
      setIsRequired(false);
      setIsPrivate(false);
    }
  }, [initialData, isOpen, initialQuestionType]);

  const handleOptionKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === "Enter" || e.key === "Tab") && currentOptionText.trim() !== "") {
      e.preventDefault();
      const newText = currentOptionText.trim();
      if (!options.find((opt) => opt.text === newText)) {
        // Check by text
        setOptions([...options, { id: crypto.randomUUID(), text: newText }]);
      }
      setCurrentOptionText("");
    }
  };

  const handleRemoveOption = (optionIdToRemove: string) => {
    // Remove by ID
    setOptions(options.filter((opt) => opt.id !== optionIdToRemove));
  };

  const handleSave = () => {
    const finalQuestionType = mapLocalToQuestionType(selectionType);
    const questionData: QuestionWithId = {
      id: initialData?.id || "temp_" + crypto.randomUUID(),
      question: questionText,
      options: options,
      type: finalQuestionType,
      isRequired,
      isPrivate, // Persist this
    };
    onSave(questionData);
    onClose(); // Close dialog after save
  };

  const showOptionsInput = selectionType === "Single" || selectionType === "Multiple";

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[525px] bg-white border-gray-200 text-gray-900">
        <DialogHeader className="pr-10">
          <DialogTitle className="text-xl font-semibold text-gray-900 flex items-center">
            {showOptionsInput && <ListFilter className="h-5 w-5 mr-2 text-gray-500" />}
            {selectionType === "Text" && <Type className="h-5 w-5 mr-2 text-gray-500" />}
            {initialData ? "Edit Question" : "Add Question"}
          </DialogTitle>
          {/* <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-5 w-5" />
            <span className="sr-only">Close</span>
          </DialogClose> */}
        </DialogHeader>

        <div className="py-4 space-y-6">
          {/* Description based on question type */}
          {showOptionsInput && (
            <div className="text-sm text-gray-500 -mt-2">Let the guest choose from a list of options</div>
          )}
          {selectionType === "Text" && (
            <div className="text-sm text-gray-500 -mt-2">Let the guest provide a text answer</div>
          )}

          <div>
            <label htmlFor="questionText" className="block text-sm font-medium text-gray-700 mb-1">
              Question
            </label>
            <Input
              id="questionText"
              value={questionText}
              onChange={(e) => setQuestionText(e.target.value)}
              placeholder="Enter your question"
              className="bg-white border-gray-300 text-gray-900 placeholder:text-gray-400"
            />
          </div>

          {/* Conditionally render Options input based on selectionType */}
          {showOptionsInput && (
            <div>
              <label htmlFor="options" className="block text-sm font-medium text-gray-700 mb-1">
                Options
              </label>
              <Input
                id="options"
                value={currentOptionText} // Renamed
                onChange={(e) => setCurrentOptionText(e.target.value)} // Renamed
                onKeyDown={handleOptionKeyDown}
                placeholder="Add options"
                className="bg-white border-gray-300 text-gray-900 placeholder:text-gray-400"
              />
              <p className="text-xs text-gray-500 mt-1">Press Enter or Tab key to add a new option.</p>
              <div className="mt-2 flex flex-wrap gap-2">
                {options.map(
                  (
                    opt // opt is an Option object
                  ) => (
                    <Badge
                      key={opt.id} // Use opt.id
                      variant="secondary"
                      className="bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200">
                      {opt.text} {/* Display opt.text */}
                      <button
                        onClick={() => handleRemoveOption(opt.id)} // Pass opt.id
                        className="ml-2 text-gray-500 hover:text-gray-700">
                        <X className="h-4 w-4" />
                      </button>
                    </Badge>
                  )
                )}
              </div>
            </div>
          )}

          {/* Selection Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Selection Type</label>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant={selectionType === "Single" ? "outline" : "outline"}
                onClick={() => setSelectionType("Single")}
                className={`${selectionType === "Single" ? "bg-blue-50 text-blue-600 border-blue-200" : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`}>
                Single
              </Button>
              <Button
                variant={selectionType === "Multiple" ? "outline" : "outline"}
                onClick={() => setSelectionType("Multiple")}
                className={`${selectionType === "Multiple" ? "bg-blue-50 text-blue-600 border-blue-200" : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`}>
                Multiple
              </Button>
              <Button
                variant={selectionType === "Text" ? "outline" : "outline"}
                onClick={() => setSelectionType("Text")}
                className={`${selectionType === "Text" ? "bg-blue-50 text-blue-600 border-blue-200" : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`}>
                Text
              </Button>
            </div>
          </div>

          {/* Required Toggle */}
          <div className="flex items-center justify-between pt-2">
            <Switch label="Required" checked={isRequired} onChange={setIsRequired} />
          </div>
        </div>

        <DialogFooter className="sm:justify-end border-t border-gray-200 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="bg-white border-gray-300 text-gray-700 hover:bg-gray-50">
            Cancel
          </Button>
          <Button type="button" onClick={handleSave} className="bg-blue-600 hover:bg-blue-700 text-white">
            {initialData ? "Save Changes" : "Add Question"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
