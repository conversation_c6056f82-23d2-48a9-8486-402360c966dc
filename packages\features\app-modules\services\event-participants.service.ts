import { prisma, type Prisma } from "@meeeetup/prisma";
import { z } from "zod";
import { searchByEmailOrNameResponseSchema } from "@/schema/host/event-participant/searchByEmailOrName";

import { errors } from "@/lib/errors";
import { EventParticipantFilterOptions } from "@/schema/filters/participant-filter";

export const getEventParticipantsByEventId = async (
  eventId: string,
  filterOptions?: EventParticipantFilterOptions
) => {
  const { page = 1, pageSize = 10, order = "createdAt:desc", searchQuery = "" } = filterOptions || {};
  // Create a separate query to get matching user IDs first

  // Then use these IDs in your main query
  const where: Prisma.EventParticipantWhereInput = {
    eventId,
    OR: [
      // Guest fields (for non-registered participants)
      { guestEmail: { contains: searchQuery, mode: "insensitive" } },
      { guestName: { contains: searchQuery, mode: "insensitive" } },

      // User basic fields
      { user: { name: { contains: searchQuery, mode: "insensitive" } } },
      { user: { email: { contains: searchQuery, mode: "insensitive" } } },

      // ParticipantProfile direct fields
      { user: { participantProfile: { name: { contains: searchQuery, mode: "insensitive" } } } },
      { user: { participantProfile: { email: { contains: searchQuery, mode: "insensitive" } } } },
      { user: { participantProfile: { phoneNumber: { contains: searchQuery, mode: "insensitive" } } } },
      { user: { participantProfile: { gender: { contains: searchQuery, mode: "insensitive" } } } },
      { user: { participantProfile: { bio: { contains: searchQuery, mode: "insensitive" } } } },

      // Work Experience fields
      {
        user: {
          participantProfile: {
            workExperience: { some: { position: { contains: searchQuery, mode: "insensitive" } } },
          },
        },
      },
      {
        user: {
          participantProfile: {
            workExperience: { some: { company: { name: { contains: searchQuery, mode: "insensitive" } } } },
          },
        },
      },

      // Education fields
      {
        user: {
          participantProfile: {
            education: { some: { university: { contains: searchQuery, mode: "insensitive" } } },
          },
        },
      },
      {
        user: {
          participantProfile: {
            education: { some: { major: { contains: searchQuery, mode: "insensitive" } } },
          },
        },
      },
      {
        user: {
          participantProfile: {
            education: { some: { degree: { contains: searchQuery, mode: "insensitive" } } },
          },
        },
      },

      {
        formSubmission: {
          some: {
            answers: {
              some: {
                answer: {
                  path: [],
                  string_contains: searchQuery,
                  mode: "insensitive",
                },
              },
            },
          },
        },
      },
    ],
    NOT: [{ status: "CANCELLED" }, { status: "PENDING" }],
  };

  const skip = (Number(page) - 1) * Number(pageSize);
  const take = Number(pageSize);
  const select = {
    id: true,
    userId: true,
    status: true,
    guestEmail: true,
    guestName: true,
    updatedAt: true,
    checkInMethod: true,
    user: {
      include: {
        participantProfile: true,
      },
    },
  };

  const eventParticipants = await prisma.eventParticipant.findMany({
    where,
    skip,
    take,
    select,
  });

  const participants = eventParticipants.map((participant) => {
    return {
      id: participant.id,
      image: participant.user?.participantProfile?.imgUrl || participant.user?.image || "",
      username: participant.user?.participantProfile?.name || participant.guestName || "",
      email: participant.user?.participantProfile?.email || participant.guestEmail || "",
      isGuest: !participant.userId,
      appliedDate: participant.updatedAt,
      status: participant.status ?? "PENDING",
      face: participant.checkInMethod === "FACE_RECOGNITION",
    };
  });

  if (order) {
    const [field, direction] = order.split(":") as [string, "asc" | "desc"];
    participants.sort((a, b) => {
      let comparison = 0;
      if (field === "username" || field === "email" || field === "status" || field === "appliedDate") {
        if (a[field] < b[field]) {
          comparison = -1;
        } else if (a[field] > b[field]) {
          comparison = 1;
        }
      }

      return direction === "desc" ? comparison * -1 : comparison;
    });
  }

  const total = await prisma.eventParticipant.count({
    where,
  });

  return { participants, total };
};

export const manualEventParticipantCheckInOut = async ({
  eventId,
  participantId,
}: {
  eventId: string;
  participantId: string;
}) => {
  const participant = await prisma.eventParticipant.findUnique({
    where: {
      eventId,
      id: participantId,
    },
  });
  if (!participant) throw errors.notFound("Participant not found!");

  let newStatus: Prisma.EventParticipantUpdateInput["status"];
  let actionType: "CHECK_IN" | "CHECK_OUT";
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const dataToUpdate: any = {};

  if (participant.status === "CHECKED_IN") {
    newStatus = "CHECKED_OUT";
    actionType = "CHECK_OUT";
    // Optionally, clear checkInTime and checkInMethod on checkout, or leave as is
    // dataToUpdate.checkInTime = null;
    // dataToUpdate.checkInMethod = null;
  } else {
    newStatus = "CHECKED_IN";
    actionType = "CHECK_IN";
    dataToUpdate.checkInTime = new Date();
    dataToUpdate.checkInMethod = "MANUAL";
  }

  dataToUpdate.status = newStatus;

  const updatedParticipant = await prisma.$transaction(async (tx) => {
    const updatedP = await tx.eventParticipant.update({
      where: {
        id: participant.id,
      },
      data: dataToUpdate,
    });

    await tx.checkInOutRecord.create({
      data: {
        participantId: participant.id,
        actionType,
        actionTime: new Date(),
        method: "MANUAL",
      },
    });
    return updatedP;
  });

  return updatedParticipant;
};

export const deleteEventParticipant = async ({
  eventId,
  participantId,
  status,
}: {
  eventId: string;
  participantId: string;
  status: "CANCELLED" | "CHECKED_IN";
}) => {
  const participant = await prisma.eventParticipant.findUnique({
    where: {
      eventId,
      id: participantId,
    },
  });
  if (!participant) throw errors.notFound("Participant not found!");

  const deletedParticipant = await prisma.eventParticipant.update({
    where: {
      id: participant.id,
    },
    data: {
      status,
    },
  });

  return deletedParticipant;
};

export const getEventParticipantDetails = async ({
  eventId,
  participantId,
}: {
  eventId: string;
  participantId: string;
}) => {
  const participant = await prisma.eventParticipant.findUnique({
    where: {
      eventId,
      id: participantId,
    },
    include: {
      user: true,
      ticket: {
        include: {
          ticketType: true,
        },
      },
      formSubmission: {
        include: {
          answers: {
            include: {
              question: true,
            },
          },
        },
      },
    },
  });
  if (!participant) throw errors.notFound("Participant not found!");

  const questionAnswers = participant.formSubmission.reduce(
    (acc, submission) => {
      submission.answers.forEach((answer) => {
        const qa = {
          question: answer.question.questionText,
          answer: answer.answer as string,
        };
        if (answer.question.isBasic) {
          acc.basic.push(qa);
        } else {
          acc.notBasic.push(qa);
        }
      });
      return acc;
    },
    {
      basic: [] as { question: string; answer: string }[],
      notBasic: [] as { question: string; answer: string }[],
    }
  );

  const participantProfile = await prisma.participantProfile.findUnique({
    where: {
      userId: participant.userId ?? "",
    },
    include: {
      workExperience: {
        include: {
          company: true,
        },
      },
      education: true,
    },
  });

  const socialLinks = (participantProfile?.socialLinks as Array<{ platform: string; url: string }>)?.map(
    (link) => {
      const formatted = {
        platform: link.platform,
        url: link.url,
      };
      return formatted;
    }
  );

  return {
    ...participant,
    profile: { ...participantProfile, socialLinks },
    questionAnswers,
  };
};

export const getEventParticipantsByHostId = async (
  hostId: string,
  filterOptions?: EventParticipantFilterOptions
) => {
  const { page = 1, pageSize = 10, order = "createdAt:desc", searchQuery = "" } = filterOptions || {};

  const orderBy: Prisma.EventParticipantOrderByWithRelationInput[] = [];
  if (order) {
    const [field, direction] = order.split(":") as [
      keyof Prisma.EventParticipantOrderByWithRelationInput | "email" | "name",
      "asc" | "desc",
    ];
    if (field === "email" || field === "name") {
      orderBy.push({ user: { [field]: direction } });
      if (field === "email") orderBy.push({ guestEmail: direction });
      if (field === "name") orderBy.push({ guestName: direction });
    } else {
      orderBy.push({ [field]: direction });
    }
  }

  const where: Prisma.EventParticipantWhereInput = {
    event: {
      OR: [{ hostId: { equals: hostId } }, { admins: { some: { hostId: hostId, active: true } } }],
    },
    OR: [
      { guestEmail: { contains: searchQuery, mode: "insensitive" } },
      { guestName: { contains: searchQuery, mode: "insensitive" } },
      { user: { name: { contains: searchQuery, mode: "insensitive" } } },
      { user: { email: { contains: searchQuery, mode: "insensitive" } } },
    ],
    NOT: [{ status: "CANCELLED" }, { status: "PENDING" }],
  };

  const skip = (Number(page) - 1) * Number(pageSize);
  const take = Number(pageSize);
  const select = {
    id: true,
    userId: true,
    status: true,
    guestEmail: true,
    guestName: true,
    updatedAt: true,
    checkInMethod: true,
    user: {
      select: {
        name: true,
        email: true,
        image: true,
        participantProfile: {
          include: {
            workExperience: {
              include: {
                company: true,
              },
            },
          },
        },
        receivedFavorites: {
          where: {
            giverId: hostId,
          },
          select: {
            id: true,
            giverId: true,
          },
        },
      },
    },
    event: true,
  };

  const eventParticipants = await prisma.eventParticipant.findMany({
    where,
    orderBy,
    skip,
    take,
    select,
  });

  type UniqueParticipantData = Omit<(typeof eventParticipants)[number], "event"> & {
    event: (typeof eventParticipants)[number]["event"][];
  };

  const uniqueParticipants: { [key: string]: UniqueParticipantData } = {};

  for (const participant of eventParticipants) {
    const { userId, guestEmail, guestName } = participant;
    const key = userId || guestEmail || guestName;
    if (!key) throw errors.internal("Invalid participant data");

    if (!uniqueParticipants[key]) {
      const { event, ...rest } = participant;
      uniqueParticipants[key] = { ...rest, event: [event] };
    } else {
      uniqueParticipants[key].event = [...uniqueParticipants[key].event, participant.event];
    }
  }

  const participants = Object.values(uniqueParticipants);

  return { participants, total: participants.length };
};

export const getParticipantProfileById = async ({
  participantId,
  hostId,
}: {
  participantId: string;
  hostId: string;
}) => {
  const participantProfile = await prisma.participantProfile.findUnique({
    where: {
      userId: participantId,
    },
    include: {
      workExperience: {
        include: {
          company: true,
        },
      },
      education: true,
      user: {
        include: {
          receivedFavorites: {
            where: {
              giverId: participantId,
            },
          },
        },
      },
    },
  });

  const eventsByYou = await prisma.event.findMany({
    where: {
      OR: [{ hostId: { equals: hostId } }, { admins: { some: { hostId: hostId, active: true } } }],
      eventParticipant: {
        some: {
          userId: participantId,
        },
      },
    },
  });

  if (!participantProfile) {
    const guestParticipant = await prisma.eventParticipant.findUnique({
      where: {
        id: participantId,
      },
      select: {
        guestEmail: true,
        guestName: true,
      },
    });

    if (!guestParticipant) throw errors.notFound("No Participant or profile found!");
    return { isGuest: true as const, guestParticipant, eventsByYou: [] };
  }

  return { isGuest: false as const, participantProfile, eventsByYou };
};

export const getAllEventsByHostAndParticipant = async ({
  hostId,
  participantId,
}: {
  hostId: string;
  participantId: string;
}) => {
  const allEventsForParticipantAndHost = await prisma.event.findMany({
    where: {
      OR: [{ hostId: { equals: hostId } }, { admins: { some: { hostId: hostId, active: true } } }],
      eventParticipant: {
        some: {
          id: participantId,
        },
      },
    },
  });

  console.log(
    `DEBUG: Found ${allEventsForParticipantAndHost?.length ?? 0} events hosted by ${hostId} with participant ${participantId}`
  );

  return allEventsForParticipantAndHost;
};

export const searchEventParticipantsByEmailOrName = async (eventId: string, emailOrName: string) => {
  const participants = await prisma.eventParticipant.findMany({
    where: {
      eventId,
      OR: [
        // Guest fields
        { guestEmail: { contains: emailOrName, mode: "insensitive" } },
        { guestName: { contains: emailOrName, mode: "insensitive" } },
        // User basic fields
        {
          user: {
            email: { contains: emailOrName, mode: "insensitive" },
          },
        },
        {
          user: {
            name: { contains: emailOrName, mode: "insensitive" },
          },
        },
        // ParticipantProfile fields
        {
          user: {
            participantProfile: {
              name: { contains: emailOrName, mode: "insensitive" },
            },
          },
        },
        {
          user: {
            participantProfile: {
              email: { contains: emailOrName, mode: "insensitive" },
            },
          },
        },
      ],
      // Consistent with other functions - exclude both CANCELLED and PENDING
      NOT: [{ status: "CANCELLED" }, { status: "PENDING" }],
    },
    select: {
      id: true,
      userId: true,
      guestEmail: true,
      guestName: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          participantProfile: {
            select: {
              name: true,
              FaceAuthData: { select: { faceRegisteredAt: true } },
            },
          },
        },
      },
    },
    // Add consistent ordering
    orderBy: [{ guestName: "asc" }, { guestEmail: "asc" }],
  });

  const formattedParticipants = await Promise.all(
    participants.map(async (p) => {
      if (!p.userId) {
        if (p.guestEmail) {
          const createdUser = await prisma.user.upsert({
            where: { email: p.guestEmail },
            update: {},
            create: {
              email: p.guestEmail,
              name: p.guestName || undefined,
              participantProfile: {
                create: {
                  email: p.guestEmail,
                  name: p.guestName || undefined,
                },
              },
              roles: {
                connect: {
                  name: "participant",
                },
              },
            },
          });

          await prisma.eventParticipant.update({
            where: { id: p.id },
            data: { userId: createdUser.id },
          });

          return {
            id: createdUser.id,
            userId: createdUser.id,
            name: createdUser.name,
            email: createdUser.email,
            image: createdUser.image,
            isFaceRegistered: false,
          };
        }
        return null;
      }

      return {
        id: p.user!.id,
        userId: p.user!.id,
        name: p.user!.name || p.user!.participantProfile?.name || p.guestName || "",
        email: p.user!.email,
        image: p.user!.image,
        isFaceRegistered: !!p.user!.participantProfile?.FaceAuthData.length,
      };
    })
  );

  const filteredFormattedParticipants = formattedParticipants.filter((p) => p !== null) as NonNullable<
    (typeof formattedParticipants)[number]
  >[];

  return { participants: filteredFormattedParticipants };
};
