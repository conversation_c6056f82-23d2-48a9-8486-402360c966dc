"use client";

import React, { useState } from "react";
import { signOut } from "next-auth/react";
//@ts-ignore
import { usePathname, useRouter } from "next/navigation";

import { useTranslations } from "next-intl";

import { MobileSideBar } from "@meeeetup/ui/dash-side-bar";

// Define the menu item types for better typing
export interface MenuItem {
  label: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  href: string;
}

export interface DashboardHeaderProps {
  menuItems: MenuItem[];
  activeRoute?: string;
  user?: {
    name: string;
    image?: string;
  };
  headerTitle?: string;
  ProfileDropdown: React.ComponentType;
  LanguageSwitcher: React.ComponentType;
  onCreateEvent?: () => void;
}

export function DashboardHeader({
  menuItems,
  activeRoute,
  user,
  headerTitle = "Dashboard",
  ProfileDropdown,
  LanguageSwitcher,
  onCreateEvent,
}: DashboardHeaderProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const showCreateEventButton = !pathname?.includes("participant/dashboard");

  return (
    <header className="w-full h-fit md:px-6">
      <div className="flex items-center justify-between">
        {/* Logo - Only show on mobile since desktop has it in sidebar */}
        <div className="flex items-center gap-4">
          <div className="md:hidden">
            <MobileSideBar
              showCreateEventButton={showCreateEventButton}
              menuItems={menuItems}
              activeRoute={activeRoute || pathname}
              onCreateEvent={() => {
                if (onCreateEvent) {
                  onCreateEvent();
                } else {
                  setIsCreateDialogOpen(true);
                }
              }}
              onLogout={async () => {
                await signOut({ redirect: false });
                router.push("/login");
              }}
            />
          </div>
          <div className="text-base md:text-h4-bold font-medium text-gray-900">{headerTitle}</div>
        </div>
        {/* Right side content */}
        <div className="flex items-center gap-2">
          <ProfileDropdown />
          <LanguageSwitcher />
        </div>
      </div>
    </header>
  );
}
