import Avatar from "../avatar";
import { Button } from "../button/Button";
import { useTranslations } from "next-intl";

interface FaceConfirmationProps {
  imageUrl: string;
  name: string;
  email?: string;
  onConfirm: () => void;
  onDeny: () => void;
  t: ReturnType<typeof useTranslations>;
}

function FaceConfirmation({ imageUrl, name, email, onConfirm, onDeny, t }: FaceConfirmationProps) {
  return (
    <div className="w-full max-w-sm mx-auto">
      {/* Modern card container with subtle shadows and rounded corners */}
      <div className=" p-8">
        {/* Header section with better typography */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">{t("title")}</h1>
          <p className="text-gray-500 text-sm leading-relaxed">{t("subtitle")}</p>
        </div>

        {/* Avatar section with enhanced styling */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            {/* Subtle ring around avatar */}
            <div className="absolute inset-0 rounded-full bg-mu-base/10 scale-110 transition-transform duration-300 hover:scale-115"></div>
            <Avatar
              src={imageUrl}
              alt={name}
              fallback={name
                .split(" ")
                .map((n) => n[0])
                .join("")
                .substring(0, 2)}
              size={100}
              className="relative z-10 ring-4 ring-white shadow-lg"
            />
          </div>
        </div>

        {/* Name section with improved typography */}
        <div className="text-center mb-16">
          <p className="text-gray-600 text-sm font-medium mb-2">{t("questionPrefix")}</p>
          <h2 className="text-xl font-semibold text-gray-900 break-words leading-tight">{name}</h2>
          {email && <p className="text-sm text-gray-500 mt-1">{email}</p>}
        </div>

        {/* Action buttons with modern styling */}
        <div className="space-y-3">
          {/* Primary confirm button */}
          <Button
            variant="primary"
            size="large"
            className="w-full h-12 text-base font-medium rounded-xl transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
            onClick={onConfirm}>
            {t("confirmButton")}
          </Button>

          {/* Secondary deny button */}
          <Button
            variant="ghost"
            size="large"
            className="w-full h-12 text-base font-medium rounded-xl text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
            onClick={onDeny}>
            {t("denyButton")}
          </Button>
        </div>

        {/* Optional subtle footer */}
        <div className="mt-6 pt-6 border-t border-gray-50">
          <p className="text-xs text-gray-400 text-center leading-relaxed">{t("privacyNote")}</p>
        </div>
      </div>
    </div>
  );
}

export default FaceConfirmation;
