import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { FaceDetector, FilesetResolver, FaceDetectorResult } from "@mediapipe/tasks-vision";

// Public interface -----------------------------------------------------------
export interface UseFacialCaptureParams {
  onCapture?: (base64: string) => void;
  facingMode?: "user" | "environment";
  initialImage?: string;
  autoCapture?: boolean;
  minDetectionConfidence?: number;
  consecutiveFramesForCapture?: number;
  preparationTimeMs?: number;
}

export interface UseFacialCaptureReturn {
  /* DOM refs */
  videoRef: React.RefObject<HTMLVideoElement | null>;
  canvasRef: React.RefObject<HTMLCanvasElement | null>;

  /* state */
  capturedImage: string | null;
  error: string | null;
  isLoading: boolean;
  isCapturing: boolean;
  isSwitchingCamera: boolean;
  currentFacingMode: "user" | "environment";
  isFlipped: boolean;

  /* preparation-phase */
  isPreparationPhase: boolean;
  preparationCountdown: number;
  isPreparationPaused: boolean;

  /* helpers */
  captureImage: () => void;
  resetCapture: () => void;
  toggleCamera: () => void;
  toggleFlip: () => void;
  skipPreparation: () => void;
  pausePreparation: () => void;
  resumePreparation: () => void;
}

// ---------------------------------------------------------------------------
const IMAGE_FORMAT = "image/png";
const CAMERA_SWITCH_DELAY_MS = 300;
const MODEL_URL =
  "https://storage.googleapis.com/mediapipe-models/face_detector/blaze_face_short_range/float16/latest/blaze_face_short_range.tflite";

export function useFacialCapture({
  onCapture,
  facingMode = "user",
  initialImage,
  autoCapture = true,
  minDetectionConfidence = 0.9,
  consecutiveFramesForCapture = 6,
  preparationTimeMs = 3000,
}: UseFacialCaptureParams = {}): UseFacialCaptureReturn {
  /* ------------------------------------------------------------------ refs */
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const faceDetectionRef = useRef<FaceDetector | null>(null);
  const detectionRafIdRef = useRef<number | null>(null);
  const toggleCameraTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const preparationTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const countdownIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const consecutiveFaceFramesRef = useRef(0);
  const isPreparationPhaseRef = useRef(autoCapture);
  const isTransitioningRef = useRef(false);

  /* ---------------------------------------------------------------- state */
  const [capturedImage, setCapturedImage] = useState<string | null>(initialImage || null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCapturing, setIsCapturing] = useState(false);
  const [isSwitchingCamera, setIsSwitchingCamera] = useState(false);
  const [currentFacingMode, setCurrentFacingMode] = useState<"user" | "environment">(facingMode);
  const [isFlipped, setIsFlipped] = useState(currentFacingMode === "user");
  const [isPreparationPhase, setIsPreparationPhase] = useState(autoCapture);
  const [preparationCountdown, setPreparationCountdown] = useState<number>(0);
  const [isPreparationPaused, setIsPreparationPaused] = useState(false);

  /* -------------------------------------------------------------- memos */
  const constraints = useMemo(
    () => ({
      video: {
        facingMode: currentFacingMode,
        width: { ideal: 1280 },
        height: { ideal: 720 },
      },
    }),
    [currentFacingMode]
  );

  /* ----------------------------------------------------------------- api */
  const startCamera = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Stop any existing tracks first.
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((t) => t.stop());
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          setIsLoading(false);
          setIsSwitchingCamera(false);
          initFaceDetection();
          if (autoCapture && !capturedImage) startPreparationPhase();
        };
      }
    } catch (e) {
      /* eslint-disable no-console */
      console.error("Error accessing camera", e);
      setError("Could not access camera. Please check permissions.");
      setIsLoading(false);
      setIsSwitchingCamera(false);
    }
  }, [constraints]);

  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((t) => t.stop());
      streamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    if (detectionRafIdRef.current) {
      cancelAnimationFrame(detectionRafIdRef.current);
      detectionRafIdRef.current = null;
    }
    if (faceDetectionRef.current) {
      faceDetectionRef.current.close();
      faceDetectionRef.current = null;
    }
  }, []);

  const captureImage = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;
    setIsCapturing(true);
    const canvas = canvasRef.current;
    const video = videoRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    const image = canvas.toDataURL(IMAGE_FORMAT);
    setCapturedImage(image);
    onCapture?.(image);
    setIsCapturing(false);
  }, [onCapture]);

  const resetCapture = useCallback(() => {
    setCapturedImage(null);
    onCapture?.("");
  }, [onCapture]);

  /* ------------------------------------------------ preparation helpers */
  const startPreparationPhase = useCallback(
    (resumeCountdown?: number) => {
      if (!autoCapture) return;

      setIsPreparationPhase(true);
      const countdownValue = resumeCountdown ?? Math.ceil(preparationTimeMs / 1000);
      setPreparationCountdown(countdownValue);

      // Cleanup older timers.
      if (preparationTimeoutRef.current) clearTimeout(preparationTimeoutRef.current);
      if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);

      // Countdown.
      countdownIntervalRef.current = setInterval(() => {
        setPreparationCountdown((prev) => {
          if (prev <= 1) {
            if (countdownIntervalRef.current) {
              clearInterval(countdownIntervalRef.current);
              countdownIntervalRef.current = null;
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      // End of phase.
      const timeoutDuration = resumeCountdown ? resumeCountdown * 1000 : preparationTimeMs;
      preparationTimeoutRef.current = setTimeout(() => {
        setIsPreparationPhase(false);
        setPreparationCountdown(0);
        if (countdownIntervalRef.current) {
          clearInterval(countdownIntervalRef.current);
          countdownIntervalRef.current = null;
        }
        preparationTimeoutRef.current = null;
      }, timeoutDuration);
    },
    [autoCapture, preparationTimeMs]
  );

  const skipPreparation = useCallback(() => {
    if (!isPreparationPhase) return;
    setIsPreparationPhase(false);
    setPreparationCountdown(0);
    if (preparationTimeoutRef.current) clearTimeout(preparationTimeoutRef.current);
    if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
  }, [isPreparationPhase]);

  const pausePreparation = useCallback(() => {
    if (!isPreparationPhase) return;
    setIsPreparationPaused(true);
    if (preparationTimeoutRef.current) clearTimeout(preparationTimeoutRef.current);
    if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
  }, [isPreparationPhase]);

  const resumePreparation = useCallback(() => {
    if (!isPreparationPhase) return;
    setIsPreparationPaused(false);
    if (preparationTimeoutRef.current) clearTimeout(preparationTimeoutRef.current);
    if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
    startPreparationPhase(preparationCountdown);
  }, [isPreparationPhase, startPreparationPhase, preparationCountdown]);

  /* ---------------------------------------------------------------- toggle */
  const toggleCamera = useCallback(() => {
    if (isSwitchingCamera) return;

    setIsSwitchingCamera(true);
    isTransitioningRef.current = true;

    const nextMode = currentFacingMode === "user" ? "environment" : "user";
    setCurrentFacingMode(nextMode);
    setIsFlipped(nextMode === "user");

    // Cancel preparation phase if active.
    skipPreparation();

    // Stop, wait, then start cam in new mode.
    stopCamera();
    if (toggleCameraTimeoutRef.current) clearTimeout(toggleCameraTimeoutRef.current);
    toggleCameraTimeoutRef.current = setTimeout(() => {
      startCamera();
      setTimeout(() => {
        isTransitioningRef.current = false;
        if (autoCapture && !capturedImage) startPreparationPhase();
      }, 1000);
      toggleCameraTimeoutRef.current = null;
    }, CAMERA_SWITCH_DELAY_MS);
  }, [
    isSwitchingCamera,
    currentFacingMode,
    skipPreparation,
    stopCamera,
    startCamera,
    autoCapture,
    capturedImage,
    startPreparationPhase,
  ]);

  const toggleFlip = useCallback(() => {
    setIsFlipped((p) => !p);
    isTransitioningRef.current = true;

    skipPreparation();

    setTimeout(() => {
      isTransitioningRef.current = false;
      if (autoCapture && !capturedImage) startPreparationPhase();
    }, 500);
  }, [skipPreparation, autoCapture, capturedImage, startPreparationPhase]);

  /* ------------------------------------------------------ face detection */
  const initFaceDetection = useCallback(async () => {
    if (faceDetectionRef.current || !videoRef.current) return;

    const fileset = await FilesetResolver.forVisionTasks(
      "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm"
    );

    const detector = await FaceDetector.createFromModelPath(fileset, MODEL_URL);

    detector.setOptions({ runningMode: "VIDEO", minDetectionConfidence });

    faceDetectionRef.current = detector;

    const handleDetections = (results: FaceDetectorResult) => {
      if (capturedImage || !autoCapture) return;
      if (results.detections?.length) {
        consecutiveFaceFramesRef.current += 1;
        if (consecutiveFaceFramesRef.current >= consecutiveFramesForCapture) {
          captureImage();
          consecutiveFaceFramesRef.current = 0;
        }
      } else {
        consecutiveFaceFramesRef.current = 0;
      }
    };

    const loop = () => {
      if (!videoRef.current || !faceDetectionRef.current) return;
      const res = faceDetectionRef.current.detectForVideo(videoRef.current, performance.now());
      if (!isPreparationPhaseRef.current && !isTransitioningRef.current) {
        handleDetections(res);
      }
      detectionRafIdRef.current = requestAnimationFrame(loop);
    };

    loop();
  }, [captureImage, capturedImage, autoCapture, minDetectionConfidence, consecutiveFramesForCapture]);

  /* ---------------------------------------------------------------- mount */
  useEffect(() => {
    isPreparationPhaseRef.current = isPreparationPhase;
  }, [isPreparationPhase]);

  useEffect(() => {
    startCamera();
    return () => {
      stopCamera();
      if (toggleCameraTimeoutRef.current) clearTimeout(toggleCameraTimeoutRef.current);
      if (preparationTimeoutRef.current) clearTimeout(preparationTimeoutRef.current);
      if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
    };
    // startCamera should be stable enough.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /* -------------------------------------------------------------- return */
  return {
    videoRef,
    canvasRef,
    capturedImage,
    error,
    isLoading,
    isCapturing,
    isSwitchingCamera,
    currentFacingMode,
    isFlipped,
    isPreparationPhase,
    preparationCountdown,
    isPreparationPaused,
    captureImage,
    resetCapture,
    toggleCamera,
    toggleFlip,
    skipPreparation,
    pausePreparation,
    resumePreparation,
  };
}
