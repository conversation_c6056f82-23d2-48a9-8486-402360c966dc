"use client";
import { LogOut, CheckCircle, LoaderIcon } from "lucide-react";
import { Button } from "@meeeetup/ui";
import { useAutoCheckoutParticipants } from "../data-hook";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@meeeetup/ui/alert-dialog";
import { useState } from "react";

interface AutoCheckoutDialogProps {
  eventId: string;
  isEventCancelled: boolean;
  onAutoCheckout: ReturnType<typeof useAutoCheckoutParticipants>["autoCheckout"];
  isCheckingOut: boolean;
  t: (key: string, options?: any) => string;
}

export const AutoCheckoutDialog = ({
  eventId,
  isEventCancelled,
  onAutoCheckout,
  isCheckingOut,
  t,
}: AutoCheckoutDialogProps) => {
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleAutoCheckout = async () => {
    onAutoCheckout("Sec_2qpgu40006calab6cgJ1tt");
    setDialogOpen(false);
  };

  return (
    <AlertDialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <AlertDialogTrigger asChild>
        <Button
          variant={"secondary"}
          disabled={isEventCancelled || isCheckingOut}
          className="flex items-center gap-2 text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-full px-4 py-2 transition-all shadow-sm hover:shadow">
          {isCheckingOut ? (
            <LoaderIcon className="size-4 animate-spin mr-1" />
          ) : (
            <>
              <LogOut className="size-4" />
              <span className="sm:inline hidden">{t("EventDetailPage.autoCheckout")}</span>
              <span className="sm:hidden inline">{t("EventDetailPage.checkoutShort")}</span>
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="sm:max-w-[500px] max-w-[95vw] p-4 sm:p-6 rounded-xl">
        <AlertDialogHeader className="mb-3 sm:mb-4">
          <AlertDialogTitle className="text-xl sm:text-2xl font-bold text-gray-800">
            {t("EventDetailPage.autoCheckoutConfirmTitle")}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">
            {t("EventDetailPage.autoCheckoutConfirmDescription")}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-2 sm:gap-3">
          <AlertDialogCancel className="h-10 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            {t("Common.cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleAutoCheckout}
            disabled={isCheckingOut}
            className="h-10 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2">
            {isCheckingOut ? (
              <>
                <LoaderIcon className="size-4 animate-spin" />
                <span>{t("Common.processing")}</span>
              </>
            ) : (
              <>
                <CheckCircle className="size-4" />
                <span>{t("Common.confirm")}</span>
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
