import Link from "next/link";
import { Calendar, ArrowRight } from "lucide-react";
import { EventFeaturedCard } from "../event-featured-card";
import { EventCardSkeleton } from "../event-card-skeleton";
import { NoEventsState } from "../no-events-state";

interface Event {
  id: string;
  name: string;
  startDatetime: string;
  bannerImageUrl?: string | null;
  location?: {
    displayName?: string;
  } | null;
}

interface ApiResponse {
  success: boolean;
  data: {
    events: Event[];
    pagination: {
      page: number;
      pageSize: number;
    };
  };
}

interface FeaturedEventsSectionProps {
  title: string;
  subtitle: string;
  isLoading: boolean;
  events?: ApiResponse;
  maxDisplayCount?: number;
  viewAllText?: string;
  viewAllHref?: string;
  noEventsTitle: string;
  noEventsMessage: string;
  noEventsButtonText?: string;
  noEventsButtonHref?: string;
}

export function FeaturedEventsSection({
  title,
  subtitle,
  isLoading,
  events,
  maxDisplayCount = 8,
  viewAllText,
  viewAllHref,
  noEventsTitle,
  noEventsMessage,
  noEventsButtonText,
  noEventsButtonHref,
}: FeaturedEventsSectionProps) {
  return (
    <section>
      <div className="text-center mb-16">
        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full px-4 py-2 mb-4 text-blue-700 text-sm font-medium">
          <Calendar className="w-4 h-4" />
          Featured Events
        </div>
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{title}</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">{subtitle}</p>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
          {[...Array(8)].map((_, index) => (
            <EventCardSkeleton key={index} />
          ))}
        </div>
      ) : events?.success && events.data.events.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
            {events.data.events.slice(0, maxDisplayCount).map((event, index) => (
              <EventFeaturedCard key={event.id} event={event} index={index} />
            ))}
          </div>

          {events.data.events.length > maxDisplayCount && viewAllText && viewAllHref && (
            <div className="text-center mt-12">
              <Link
                href={viewAllHref}
                className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-700 text-white px-8 py-4 rounded-2xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
                {viewAllText}
                <ArrowRight className="w-5 h-5" />
              </Link>
            </div>
          )}
        </>
      ) : (
        <NoEventsState
          title={noEventsTitle}
          message={noEventsMessage}
          buttonText={noEventsButtonText}
          buttonHref={noEventsButtonHref}
        />
      )}
    </section>
  );
}
