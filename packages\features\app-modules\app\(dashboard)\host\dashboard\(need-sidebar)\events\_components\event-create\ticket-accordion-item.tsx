"use client";

import * as React from "react";
import { Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";

import { Button } from "@meeeetup/ui/button";
import { AccordionTrigger, AccordionContent } from "@meeeetup/ui/accordian";
import { Input } from "@meeeetup/ui/input";
import { Textarea } from "@meeeetup/ui/textarea";

import { CreateEventTicketTypesClient } from "../../event-client-types";
import { formatPrice } from "./ticket-utils";

interface TicketAccordionItemProps {
  ticket: CreateEventTicketTypesClient;
  index: number;
  onUpdateTicket: (updates: Partial<CreateEventTicketTypesClient>) => void;
  onRemoveTicket: () => void;
  onDuplicateTicket: () => void;
}

export function TicketAccordionItem({
  ticket,
  index,
  onUpdateTicket,
  onRemoveTicket,
  onDuplicateTicket,
}: TicketAccordionItemProps) {
  const t = useTranslations("TicketAccordionItem");

  return (
    <>
      <AccordionTrigger asChild className="p-6 w-full hover:no-underline focus:no-underline">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 text-left w-full">
          {/* Main content block */}
          <div className="flex flex-col lg:flex-row lg:items-center gap-4 flex-grow min-w-0">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-2">
                <div className={`w-3 h-3 rounded-full ${ticket.isPaid ? "bg-blue-500" : "bg-green-500"}`} />
                <h3 className="text-lg font-semibold text-gray-900 truncate">{ticket.name}</h3>
                <span
                  className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                    ticket.isPaid
                      ? "bg-blue-100 text-blue-700 border border-blue-200"
                      : "bg-green-100 text-green-700 border border-green-200"
                  }`}>
                  {ticket.isPaid ? t("statusLabels.paid") : t("statusLabels.free")}
                </span>
              </div>
              <p className="text-gray-600 text-sm line-clamp-2">{ticket.description}</p>
            </div>
            <div className="flex items-center gap-6 text-sm">
              <div className="text-center">
                <div className="font-semibold text-gray-900">{ticket.quantity.toLocaleString()}</div>
                <div className="text-gray-500">{t("summaryLabels.available")}</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900">
                  {ticket.isPaid ? formatPrice(ticket.price) : t("summaryLabels.free")}
                </div>
                <div className="text-gray-500">{t("summaryLabels.price")}</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900">
                  {formatPrice(ticket.price * ticket.quantity)}
                </div>
                <div className="text-gray-500">{t("summaryLabels.revenue")}</div>
              </div>
            </div>
          </div>

          {/* Right side actions: Remove Button */}
          <div className="flex items-center flex-shrink-0">
            {/* Remove Button */}
            <Button
              variant="ghost"
              size="icon"
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                onRemoveTicket();
              }}
              className="text-gray-400 hover:text-red-600 hover:bg-red-50 focus-visible:ring-offset-0 focus-visible:ring-2 focus-visible:ring-red-500 rounded-md p-1 ml-1"
              aria-label={t("actions.removeTicket")}>
              <Trash2 className="h-5 w-5 text-red-600" />
            </Button>
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent>
        <div className="border-t border-gray-100 p-6 space-y-6 bg-gray-50/50">
          {/* Form Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Ticket Name */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t("formLabels.ticketName")} <span className="text-red-500">{t("formLabels.required")}</span>
              </label>
              <Input
                type="text"
                value={ticket.name}
                onChange={(e) => onUpdateTicket({ name: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
                placeholder={t("placeholders.ticketName")}
              />
            </div>

            {/* Price */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t("formLabels.price")}</label>
              {ticket.isPaid ? (
                <div className="relative">
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-500 font-medium">
                    {t("currencySymbol")}
                  </span>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={ticket.price}
                    onChange={(e) => onUpdateTicket({ price: Math.max(0, Number(e.target.value)) })}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                    placeholder={t("placeholders.price")}
                  />
                </div>
              ) : (
                <div className="px-4 py-3 bg-green-50 text-green-700 rounded-xl font-medium text-center border border-green-200">
                  {t("statusLabels.free")}
                </div>
              )}
            </div>

            {/* Quantity */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t("formLabels.availableQuantity")}
              </label>
              <div className="relative">
                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={() => onUpdateTicket({ quantity: Math.max(1, ticket.quantity - 1) })}
                    disabled={ticket.quantity <= 1}
                    className="flex items-center justify-center w-full h-12 text-gray-600 hover:text-gray-800 hover:bg-gray-50 disabled:text-gray-300 disabled:cursor-not-allowed transition-colors rounded-l-xl border border-r-0 border-gray-300 bg-white">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                    </svg>
                  </button>
                  <input
                    type="number"
                    min="1"
                    value={ticket.quantity}
                    onChange={(e) => onUpdateTicket({ quantity: Math.max(1, Number(e.target.value) || 1) })}
                    className="w-full h-12 px-4 text-center border-t border-b border-gray-300 bg-white focus:outline-none  [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                    placeholder={t("placeholders.quantity")}
                  />
                  <button
                    type="button"
                    onClick={() => onUpdateTicket({ quantity: ticket.quantity + 1 })}
                    className="flex items-center justify-center w-full h-12 text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-colors rounded-r-xl border border-l-0 border-gray-300 bg-white">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="lg:col-span-3">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t("formLabels.description")}
              </label>
              <Textarea
                value={ticket.description}
                onChange={(e) => onUpdateTicket({ description: e.target.value })}
                rows={3}
                className="w-full px-4 py-3 rounded-xl transition-all duration-200 resize-none bg-white"
                placeholder={t("placeholders.description")}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-wrap items-center justify-between gap-4 pt-4 border-t border-gray-200">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="small"
                type="button"
                onClick={onDuplicateTicket}
                className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
                {t("actions.duplicate")}
              </Button>

              <Button
                variant="ghost"
                size="small"
                type="button"
                onClick={() => onUpdateTicket({ isPaid: !ticket.isPaid, price: ticket.isPaid ? 0 : 25 })}
                className="flex items-center gap-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                  />
                </svg>
                {ticket.isPaid ? t("actions.makeFree") : t("actions.makePaid")}
              </Button>
            </div>

            <Button
              variant="ghost"
              size="small"
              type="button"
              onClick={onRemoveTicket}
              className="text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-300">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
              {t("actions.deleteTicket")}
            </Button>
          </div>
        </div>
      </AccordionContent>
    </>
  );
}
