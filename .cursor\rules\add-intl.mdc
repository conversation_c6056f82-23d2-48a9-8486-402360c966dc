---
description: 
globs: 
alwaysApply: false
---
## 1. Manage English Source Translations for the Web App (`public/messages/en.json`)

This file is the cornerstone of your translation efforts for the "web app."

* **Primary Source of Truth:** Treat the web app's `public/messages/en.json` as the definitive source for all English translatable strings for this application.
* **Comprehensiveness:** Ensure it includes all UI text, messages, labels, and any other user-facing strings.
* **Logical Structure:** Organize your JSON keys by feature, page, or component. This makes it easier to find and manage translations. For example:
    * `HomePage.title`
    * `Auth.loginButton`
    * `UserProfile.followersCount`
* **Reuse Existing Namespaces (Critical):**
    * Before adding new translation keys, always check if the component or feature you're working on already has an established namespace.
    * For instance, if you find a component like `RegisterEventPage` using `const t = useTranslations("EventRegistration");`, any new translatable text specific to that page or its related child components (e.g., "Event Application Form Information", "Basic Information", "Select Ticket", "Confirm" button) should be added under the existing `EventRegistration` namespace in your `en.json`.
* **How to update:** When adding new strings or refining existing ones, build upon the current structure. Add new keys under the appropriate namespaces or modify existing English values. **It's crucial not to delete existing keys, as they might be in use by other parts of the application or shared modules.**
* **Example for `EventRegistration` namespace:**
    ```json
    "EventRegistration": {
    "formTitle": "Event Application Form Information",
    "formSubtitle": "Please fill form information to know about you",
    "basicInfoSectionTitle": "Basic Information",
    "additionalInfoSectionTitle": "Additional Information",
    "ticketSectionTitle": "Select Ticket",
    "ticketSubtitle": "Choose your ticket type to complete your registration",
    "legalSectionTitle": "Legal Agreement",
    "agreeToText": "I agree to ",
    "privacyPolicyLink": "privacy policy",
    "privacyPolicyDialogTitle": "Privacy Policy",
    "termsOfUseLink": "terms of use",
    "termsOfUseDialogTitle": "Terms of Use",
    "submitButton": "Confirm Application",
    "processingButton": "Processing...",
    "statusProcessing": "Processing your registration..."
    // Add other related keys for RegisterEventPage here
    }
    ```
* **ICU Message Format:** Utilize `next-intl`'s support for ICU Message Format for dynamic translations like plurals, gender, or interpolating values.
    * Example: `"followersCount": "{count, plural, one {One follower} other {# followers}}"`
    * Example: `"greeting": "Hello {name}!"`

**Example `public/messages/en.json` structure for the Web App:**
```json
{
"HomePage": {
"title": "Welcome to My Web App",
"description": "This is the home page of the web application."
},
"Auth": {
"loginButton": "Login",
"logoutButton": "Logout"
},
"UserProfile": {
"followersCount": "{count, plural, one {One follower} other {# followers}}",
"greeting": "Hello {name}!"
},
"ParticipantEventsPage": {
"pageTitle": "My Spaces",
"loadingError": "Failed to load booking data",
"retryButton": "Retry",
"noEvents": "You haven't booked any spaces yet.",
"pagination": {
"previous": "Previous",
"pageInfo": "Page {currentPage} of {totalPages}",
"next": "Next"
},
"upcomingEvents": { // Example nested structure
"title": "Upcoming Bookings",
"countText": "{count, plural, zero {No upcoming bookings} one {One upcoming booking} other {# upcoming bookings}}"
}
// ... other keys for ParticipantEventsPage
},
"EventRegistration": {
"formTitle": "Event Application Form Information",
"formSubtitle": "Please fill form information to know about you",
"basicInfoSectionTitle": "Basic Information",
"additionalInfoSectionTitle": "Additional Information",
"ticketSectionTitle": "Select Ticket",
"ticketSubtitle": "Choose your ticket type to complete your registration",
"legalSectionTitle": "Legal Agreement",
"agreeToText": "I agree to ",
"privacyPolicyLink": "privacy policy",
"privacyPolicyDialogTitle": "Privacy Policy",
"termsOfUseLink": "terms of use",
"termsOfUseDialogTitle": "Terms of Use",
"submitButton": "Confirm Application",
"processingButton": "Processing...",
"statusProcessing": "Processing your registration..."
// ... other related keys for RegisterEventPage
}
// ... other namespaces
}
```

---

## 2. Manage Japanese Placeholder Translations (`public/messages/ja.json`) for the Web App

* Create or update `public/messages/ja.json` in the web app.
* **Crucially, populate this file by applying only the *changes* from the web app's `public/messages/en.json` that you just finalized.** This means if a new key or a modified English value exists in `en.json` but not in `ja.json`, add/update it in `ja.json`. Existing, unchanged keys in `ja.json` should remain as they are.
* **Do NOT translate the content into Japanese at this stage.** The English text from `en.json` serves as placeholders for any *new* or *modified* entries. This file will be handed off for professional Japanese translation later.

---

## 3. Adapt Translations for the Coworking App

Now, turn your attention to the "coworking app."

* **Create Coworking App's `public/messages/en.json`:**
    * Start by **copying the entire JSON structure and all keys** from the "web app's" `public/messages/en.json` into the "coworking app's" `public/messages/en.json`. This ensures that shared components from `app-modules` have consistent translation keys.
    * **Adapt English Values for Coworking Context:**
        * Carefully review all the English text *values* in the "coworking app's" `en.json`.
        * **Modify these values where necessary** to reflect the specific terminology, branding, and context of the "coworking app." Even if components are shared, the displayed text often needs to be different.
        * **Important:** When adapting, you are primarily changing the *string values* associated with the keys. Preserve the key names themselves for consistency with shared components, unless a feature is drastically different and warrants a new key (which should also be considered for the web app if it's a shared component addition).
    * **Example Adaptation for `ParticipantEventsPage` namespace:**
        * Web App: `"pageTitle": "My Spaces"`
        * Coworking App: `"pageTitle": "My Desk Reservations"` (Value changed)
        * Web App: `"noEvents": "You haven't booked any spaces yet."`
        * Coworking App: `"noEvents": "You haven't reserved any desks yet."` (Value changed)
    * **Example Adaptation for `EventRegistration` namespace:**
        * If the coworking app calls "events" "workshops":
        * Web App: `"formTitle": "Event Application Form Information"`
        * Coworking App: `"formTitle": "Workshop Registration Form"` (Value changed)
* **Create Coworking App's `public/messages/ja.json`:**
    * Once the "coworking app's" `en.json` is fully adapted, create or update its `public/messages/ja.json`.
    * This `ja.json` file should be populated by applying only the *changes* from the coworking app's *adapted* `en.json` file. This means if a new key or a modified English value exists in the coworking app's `en.json` but not in its `ja.json`, add/update it in the coworking app's `ja.json`. Existing, unchanged keys in the coworking app's `ja.json` should remain as they are. Again, it will contain English text as placeholders for new/modified entries, ready for its separate Japanese translation process.

---

## 4. Examine Existing `next-intl` Implementations

Before writing new i18n-related code, investigate how `next-intl` is currently used in your project, especially within the `app-modules` package and any existing app-specific views.

* **Message Loading:**
    * Understand how message files from the `public/messages` directory are loaded for each application. This is typically configured in:
        * Your `next-intl` setup file (often `i18n.ts` or similar).
        * Next.js middleware (`middleware.ts`).
        * Possibly within `next.config.js`.
    * Verify the configuration for both the web app and the coworking app ensures they load their respective message files.
* **Identify Common Patterns:**
    * Look for how `useTranslations` (Client Components), `getTranslations` (Server Components), and `useFormatter` are used.
    * Check for usage of `next-intl/link` or `next-intl/navigation` for localized routing.
* **Consistency is Key:** Adhere strictly to these established patterns when adding new translations to components. This maintains code quality and predictability.

---

## 5. Implement Translations in Components (for both apps)

With your message files prepared and a clear understanding of existing patterns, you can now integrate translations into your React components.

* **Use `next-intl` Hooks/Functions:**
    * In Client Components: `import { useTranslations } from 'next-intl';`
    * In Server Components: `import { getTranslations } from 'next-intl/server';`
* **App-Specific Configuration:** Ensure each app's `next-intl` configuration correctly points to its own set of message files (e.g., web app loads from `public/messages/web-app/en.json` if you decide to segregate further, or simply its root `public/messages/en.json` if each app has its own distinct `public` folder structure as implied). The prompt suggests each app has its own `public/messages`, so this should be straightforward.
* **Interpolation:** Pass dynamic values to your translation strings: `t('greeting', { name: userName })`.

**Example Client Component using `useTranslations` (can be in `app-modules` or app-specific):**
```tsx
'use client';

import { useTranslations } from 'next-intl';

export default function UserProfileSection() {
// Assuming 'UserProfile' is a defined namespace
const userT = useTranslations('UserProfile');
// Assuming 'ParticipantEventsPage' might have some relevant strings used here
// Or use a more specific namespace if appropriate for this section
const eventsT = useTranslations('ParticipantEventsPage');

const followerCount = 5;
const userName = "Alice";
const upcomingBookingCount = 3; // Example data, could come from props or state

return (
<div>
<h2>{userT('greeting', { name: userName })}</h2>
<p>{userT('followersCount', { count: followerCount })}</p>

{/* Example usage if this component shows event counts/summaries */}
{/* Ensure 'upcomingEvents.title' and 'upcomingEvents.countText' exist in ParticipantEventsPage namespace */}
<h3>{eventsT('upcomingEvents.title')}</h3>
<p>{eventsT('upcomingEvents.countText', { count: upcomingBookingCount })}</p>
</div>
);
}
```

**Example Server Component using `getTranslations` (can be in `app-modules` or app-specific):**
```tsx
import { getTranslations } from 'next-intl/server';

// Define a 'GenericPage' namespace in both apps' message files.
// The values for 'title' or 'content' can differ per app.
// e.g., web-app/en.json: "GenericPage": { "title": "Web App Info" }
// e.g., coworking-app/en.json: "GenericPage": { "title": "Coworking Space Details" }

export default async function GenericInfoPage({ appName }: { appName: string }) {
// 'locale' would typically be inferred by next-intl middleware
// For getTranslations, you often pass the namespace directly.
const t = await getTranslations('GenericPage');

return (
<div>
<h1>{t('title')} - {appName}</h1> {/* appName is just for demo here */}
<p>{t('content')}</p>
</div>
);
}
```
*(Remember to define the `GenericPage` namespace and its keys in your respective `en.json` files if you use this example.)*

---

By following these steps methodically, you'll establish a robust and maintainable internationalization system for both your web app and coworking app, ensuring consistency for shared components while allowing for context-specific language. This approach also prepares your Japanese translation files (`ja.json`) for a smooth handoff to translators by only applying necessary updates.