{
  "extends": "@meeeetup/typescript-config/nextjs.json",
  "compilerOptions": {
    "baseUrl": ".",
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": [
        "../../packages/features/app-modules/*"
      ],
      // "@meeeetup/prisma": [
      //   "../../packages/coworking-prisma/src/index.ts"
      // ]
    },
    "target": "ES2017"
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "next-env.d.ts",
    "next.config.js",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}
