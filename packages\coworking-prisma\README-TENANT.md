# Tenant-Aware Prisma Middleware

This package provides automatic tenant filtering for your Prisma queries based on the `App` table. It ensures that all Event-related queries are automatically scoped to a specific application context.

## Features

- 🔒 **Automatic Filtering**: All Event queries are automatically filtered by `appId`
- 🌐 **Context Management**: Uses AsyncLocalStorage for request-scoped tenant context
- 🛡️ **Security**: Prevents cross-tenant data access
- 🔗 **Relationship Filtering**: Automatically filters related models (EventParticipant, Ticket, etc.)
- 🎯 **Flexible API**: Multiple ways to work with tenant-scoped data
- 🚀 **Easy Integration**: Simple setup with existing Prisma workflows

## Quick Start

### 1. Basic Usage with Context

```typescript
import { withAppContext, prisma } from "@your-org/coworking-prisma";

// All queries within this context are automatically filtered by appId
const events = await withAppContext("your-app-id", async () => {
  return prisma.event.findMany({
    include: {
      host: true,
      ticketTypes: true,
      eventParticipant: true,
    },
  });
});
```

### 2. Using the Tenant Client

```typescript
import { createTenantClient } from "@your-org/coworking-prisma";

const tenantClient = createTenantClient("your-app-id");

// All operations are automatically scoped to your-app-id
const events = await tenantClient.getEvents({
  where: { status: "PUBLISHED" },
});

const newEvent = await tenantClient.createEvent({
  hostId: "host-123",
  name: "My Event",
  // appId is automatically set
});
```

### 3. Express/Next.js Middleware

```typescript
import { createTenantMiddleware } from "@your-org/coworking-prisma";

const tenantMiddleware = createTenantMiddleware();

// Express
app.use(tenantMiddleware);

// Next.js API Route
export default async function handler(req, res) {
  return new Promise((resolve) => {
    tenantMiddleware(req, res, async () => {
      const events = await prisma.event.findMany();
      res.json(events);
      resolve();
    });
  });
}
```

## How It Works

The middleware uses Prisma's `$use` hook to intercept all database operations and automatically:

1. **Filter queries** by adding `appId` to WHERE clauses
2. **Set appId** on CREATE operations
3. **Validate access** on UPDATE/DELETE operations
4. **Filter related models** through relationship chains

### Supported Models

#### Direct Filtering (by appId)

- `Event` - Has direct `appId` field

#### Relationship Filtering

- `EventParticipant` - Filtered through Event relationship
- `Ticket` - Filtered through Event relationship
- `TicketType` - Filtered through Event relationship
- `Admin` - Filtered through Event relationship
- `Form` - Filtered through Event relationship
- `FormSubmission` - Filtered through Form → Event chain
- `Question` - Filtered through Form → Event chain
- `FormAnswer` - Filtered through FormSubmission → Form → Event chain
- `Favorite` - Filtered through Event relationship
- `Note` - Filtered through EventParticipant → Event chain
- `CheckInOutRecord` - Filtered through EventParticipant → Event chain

## Context Management

The system uses Node.js `AsyncLocalStorage` to maintain tenant context throughout the request lifecycle.

### Setting Context

```typescript
// Method 1: Manual context setting
await withAppContext("app-123", async () => {
  // All Prisma operations here use app-123
});

// Method 2: Tenant client
const client = createTenantClient("app-123");
await client.execute(async () => {
  // All operations scoped to app-123
});

// Method 3: Middleware (automatic)
// Context set based on headers, subdomain, or URL path
```

### Context Sources

The middleware can extract `appId` from:

1. **HTTP Headers**: `X-App-Id`
2. **Query Parameters**: `?appId=your-app-id`
3. **Subdomain**: `app-123.yourdomain.com` → `app-123`
4. **URL Path**: `/app/app-123/events` → `app-123`

### Getting Current Context

```typescript
import { getCurrentAppId } from "@your-org/coworking-prisma";

const currentAppId = getCurrentAppId();
// Returns current appId or null if no context set
```

## API Reference

### Core Functions

#### `withAppContext<T>(appId: string, fn: () => T | Promise<T>): T | Promise<T>`

Execute a function with a specific app context.

#### `getCurrentAppId(): string | null`

Get the current app ID from context.

#### `createTenantClient(appId: string): TenantPrismaClient`

Create a tenant-scoped client for easier API usage.

#### `createTenantMiddleware()`

Create Express/Next.js middleware for automatic context setting.

### TenantPrismaClient Methods

```typescript
const client = createTenantClient('app-id');

// Convenience methods
await client.getEvents(args?)
await client.getEvent(args)
await client.createEvent(data)
await client.updateEvent(args)
await client.deleteEvent(args)
await client.getEventParticipants(args?)
await client.getTickets(args?)
await client.getTicketTypes(args?)

// Generic execution
await client.execute(() => {
  // Any Prisma operations
});
```

## Examples

### Creating Events

```typescript
// Automatic appId setting
const event = await withAppContext("app-123", async () => {
  return prisma.event.create({
    data: {
      hostId: "host-456",
      name: "Tech Meetup",
      startDatetime: new Date(),
      endDatetime: new Date(Date.now() + 3600000),
      // appId: 'app-123' is automatically added
    },
  });
});
```

### Querying with Complex Filters

```typescript
const events = await withAppContext("app-123", async () => {
  return prisma.event.findMany({
    where: {
      OR: [{ status: "PUBLISHED" }, { status: "LIMITED_PUBLISHED" }],
      startDatetime: { gte: new Date() },
      // appId: 'app-123' is automatically added
    },
    include: {
      eventParticipant: true,
      ticketTypes: true,
    },
  });
});
```

### Working with Related Data

```typescript
// All related queries are automatically filtered
const participants = await withAppContext("app-123", async () => {
  return prisma.eventParticipant.findMany({
    include: {
      event: true, // Only events from app-123
      user: true,
      ticket: true,
    },
  });
});
```

## Security Considerations

1. **Context Validation**: Always validate that the user has access to the specified app
2. **Escape Hatch**: Direct Prisma client access bypasses filtering
3. **Related Models**: Ensure proper filtering for all relationship chains
4. **Error Handling**: Handle cases where no context is set

## Best Practices

1. **Always Use Context**: Set tenant context at the request boundary
2. **Use Tenant Client**: For cleaner API in service layers
3. **Validate Access**: Check user permissions before setting context
4. **Handle Errors**: Gracefully handle missing context scenarios
5. **Test Thoroughly**: Ensure filtering works across all use cases

## Troubleshooting

### Common Issues

1. **No Filtering Applied**

   - Ensure context is set with `withAppContext` or middleware
   - Check that `getCurrentAppId()` returns the expected value

2. **Related Models Not Filtered**

   - Verify the model is in the `eventRelatedModels` array
   - Check relationship chain configuration

3. **Performance Issues**
   - Add database indexes on `appId` and related foreign keys
   - Monitor query execution plans

### Debug Mode

Enable query logging to see how filters are applied:

```typescript
const prisma = new PrismaClient({
  log: ["query", "info", "warn", "error"],
});
```

## Migration Guide

If you're adding tenant filtering to an existing system:

1. **Add appId to existing Event records**
2. **Update application code** to use tenant context
3. **Add database indexes** for performance
4. **Test thoroughly** with existing data
5. **Consider migration scripts** for data consistency

## Contributing

When extending the tenant filtering system:

1. Add new models to the appropriate filtering arrays
2. Update relationship chain logic for complex models
3. Add tests for new filtering scenarios
4. Update documentation and examples
