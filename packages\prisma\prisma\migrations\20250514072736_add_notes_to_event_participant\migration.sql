-- CreateEnum
CREATE TYPE "NoteType" AS ENUM ('PARTICIPANT_TO_PARTICIPANT', 'HOST_TO_PARTICIPANT');

-- CreateTable
CREATE TABLE "notes" (
    "id" TEXT NOT NULL,
    "eventParticipantId" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "type" "NoteType" NOT NULL DEFAULT 'HOST_TO_PARTICIPANT',
    "writtenBy" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notes_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "notes" ADD CONSTRAINT "notes_eventParticipantId_fkey" FOREIGN KEY ("eventParticipantId") REFERENCES "event_participants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
