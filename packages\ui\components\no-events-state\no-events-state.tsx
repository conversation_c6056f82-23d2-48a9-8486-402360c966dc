import Link from "next/link";
import { Calendar, Sparkles, ArrowRight } from "lucide-react";

interface NoEventsStateProps {
  title: string;
  message: string;
  buttonText?: string;
  buttonHref?: string;
}

export function NoEventsState({ title, message, buttonText, buttonHref }: NoEventsStateProps) {
  return (
    <div className="text-center py-20">
      <div className="relative inline-block mb-8">
        <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto">
          <Calendar className="w-16 h-16 text-blue-500" />
        </div>
        <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
          <Sparkles className="w-4 h-4 text-white" />
        </div>
      </div>

      <h3 className="text-3xl font-bold text-gray-800 mb-4">{title}</h3>
      <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">{message}</p>

      {buttonText && buttonHref && (
        <div className="space-y-4">
          <Link
            href={buttonHref}
            className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
            {buttonText}
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>
      )}
    </div>
  );
}
