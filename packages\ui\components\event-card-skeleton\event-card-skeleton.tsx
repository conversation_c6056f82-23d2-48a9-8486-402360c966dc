interface EventCardSkeletonProps {
  className?: string;
}

export function EventCardSkeleton({ className = "" }: EventCardSkeletonProps) {
  return (
    <div
      className={`group relative bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 animate-pulse ${className}`}>
      <div className="relative">
        <div className="bg-gray-200 h-56 w-full"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
      </div>
      <div className="p-6">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-12 h-4 bg-gray-200 rounded-full"></div>
          <div className="w-20 h-4 bg-gray-200 rounded-full"></div>
        </div>
        <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
        <div className="flex items-center justify-between">
          <div className="h-4 bg-gray-200 rounded w-16"></div>
          <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
