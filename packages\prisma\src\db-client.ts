/**
 * Type-safe Prisma Client with Tenant Filtering using Extensions
 *
 * This module provides a Prisma client with comprehensive type safety and tenant filtering
 * using the modern Prisma Client extensions API instead of deprecated middleware.
 * It automatically syncs with Prisma schema changes by using generated types.
 *
 * Usage Examples:
 *
 * ```typescript
 * import { prisma, isEventRelatedModel, TenantFilterError } from './db-client';
 *
 * // Runtime model validation using Prisma types
 * if (isEventRelatedModel(someModelName)) {
 *   // TypeScript knows someModelName is a valid event-related model
 * }
 *
 * // Error handling
 * try {
 *   await prisma.note.findMany();
 * } catch (error) {
 *   if (error instanceof TenantFilterError) {
 *     console.log(`Tenant filter error on ${error.model} with action ${error.action}`);
 *   }
 * }
 * ```
 *
 * Key Features:
 * - Uses Prisma's generated types for automatic schema sync
 * - Type guards for runtime model type checking
 * - Automatic tenant filtering based on app context using extensions
 * - Comprehensive error handling with custom error types
 * - Runtime validation to catch configuration errors early
 */

import { PrismaClient, Prisma } from "@prisma/client";
import { AsyncLocalStorage } from "async_hooks";

// Constants
const DEFAULT_APP_ID = "cmbj0vhaa0000syj89n6mumpm";
const COWORKING_APP_ID = "cmbk6svs60000sy7w2pcxo2we";

// Async local storage for app context
const appContextStorage = new AsyncLocalStorage<{ appId: string }>();

// Use Prisma's generated ModelName enum for type safety
type PrismaModelName = Prisma.ModelName;

// Define relationship configurations using Prisma types
interface RelationshipConfig {
  relationName: string;
  foreignKeyField: string;
}

// Map models to their relationship configurations using Prisma's enum values
const MODEL_RELATIONSHIP_CONFIG: Partial<Record<PrismaModelName, RelationshipConfig>> = {
  [Prisma.ModelName.Note]: {
    relationName: "EventParticipant",
    foreignKeyField: "eventParticipantId",
  },
  [Prisma.ModelName.CheckInOutRecord]: {
    relationName: "participant",
    foreignKeyField: "participantId",
  },
} as const;

// Model configuration for tenant filtering using Prisma's ModelName enum
const TENANT_CONFIG = {
  // Models that are directly filtered by appId
  directlyFiltered: [Prisma.ModelName.Event] as const,

  // Models related to Event through direct foreign key
  eventRelated: {
    direct: [
      Prisma.ModelName.EventParticipant,
      Prisma.ModelName.Ticket,
      Prisma.ModelName.TicketType,
      Prisma.ModelName.Admin,
      Prisma.ModelName.Form,
      Prisma.ModelName.Favorite,
    ] as const,
    // Models related through other relationships
    nested: {
      // Form -> Event
      throughForm: [Prisma.ModelName.FormSubmission, Prisma.ModelName.Question] as const,
      // FormSubmission -> Form -> Event
      throughFormSubmission: [Prisma.ModelName.FormAnswer] as const,
      // EventParticipant -> Event
      throughEventParticipant: [Prisma.ModelName.CheckInOutRecord, Prisma.ModelName.Note] as const,
    },
  },
} as const;

// Type definitions for better type safety
type DirectlyFilteredModel = (typeof TENANT_CONFIG.directlyFiltered)[number];
type EventRelatedDirectModel = (typeof TENANT_CONFIG.eventRelated.direct)[number];
type EventRelatedThroughFormModel = (typeof TENANT_CONFIG.eventRelated.nested.throughForm)[number];
type EventRelatedThroughFormSubmissionModel =
  (typeof TENANT_CONFIG.eventRelated.nested.throughFormSubmission)[number];
type EventRelatedThroughEventParticipantModel =
  (typeof TENANT_CONFIG.eventRelated.nested.throughEventParticipant)[number];

type EventRelatedModel =
  | EventRelatedDirectModel
  | EventRelatedThroughFormModel
  | EventRelatedThroughFormSubmissionModel
  | EventRelatedThroughEventParticipantModel;

// Type guards for model checking
const isDirectlyFilteredModel = (model: string): model is DirectlyFilteredModel => {
  return TENANT_CONFIG.directlyFiltered.includes(model as DirectlyFilteredModel);
};

const isEventRelatedDirectModel = (model: string): model is EventRelatedDirectModel => {
  return TENANT_CONFIG.eventRelated.direct.includes(model as EventRelatedDirectModel);
};

const isEventRelatedThroughFormModel = (model: string): model is EventRelatedThroughFormModel => {
  return TENANT_CONFIG.eventRelated.nested.throughForm.includes(model as EventRelatedThroughFormModel);
};

const isEventRelatedThroughFormSubmissionModel = (
  model: string
): model is EventRelatedThroughFormSubmissionModel => {
  return TENANT_CONFIG.eventRelated.nested.throughFormSubmission.includes(
    model as EventRelatedThroughFormSubmissionModel
  );
};

const isEventRelatedThroughEventParticipantModel = (
  model: string
): model is EventRelatedThroughEventParticipantModel => {
  return TENANT_CONFIG.eventRelated.nested.throughEventParticipant.includes(
    model as EventRelatedThroughEventParticipantModel
  );
};

const isEventRelatedModel = (model: string): model is EventRelatedModel => {
  return (
    isEventRelatedDirectModel(model) ||
    isEventRelatedThroughFormModel(model) ||
    isEventRelatedThroughFormSubmissionModel(model) ||
    isEventRelatedThroughEventParticipantModel(model)
  );
};

// Custom error types for better error handling
class TenantFilterError extends Error {
  constructor(
    message: string,
    public readonly model: string,
    public readonly action: string
  ) {
    super(`[TenantFilter] ${message} (Model: ${model}, Action: ${action})`);
    this.name = "TenantFilterError";
  }
}

class ModelConfigurationError extends Error {
  constructor(
    message: string,
    public readonly model: string
  ) {
    super(`[ModelConfiguration] ${message} (Model: ${model})`);
    this.name = "ModelConfigurationError";
  }
}

// Global Prisma instance
const globalForPrisma = global as unknown as { prisma: ReturnType<typeof createPrismaClient> };

// Helper functions
const APP_ID = process.env.APP_ID || COWORKING_APP_ID;
export const getCurrentAppId = (): string => {
  const context = appContextStorage.getStore();
  return context?.appId || APP_ID;
};

export const withAppContext = <T>(appId: string, fn: () => T | Promise<T>): T | Promise<T> => {
  return appContextStorage.run({ appId }, fn);
};

/**
 * Adds appId filter to where clauses with support for complex queries
 */
const addAppIdFilter = (where: any, appId: string): any => {
  if (!where) {
    return { appId };
  }

  if (Array.isArray(where)) {
    return where.map((w) => ({ ...w, appId }));
  }

  if (typeof where === "object") {
    // Handle OR/AND/NOT operations
    if (where.OR) {
      return { ...where, OR: where.OR.map((w: any) => ({ ...w, appId })) };
    }
    if (where.AND) {
      return { ...where, AND: [...where.AND, { appId }] };
    }
    if (where.NOT) {
      return { ...where, NOT: { ...where.NOT, appId: { not: appId } } };
    }

    return { ...where, appId };
  }

  return { appId };
};

/**
 * Adds event relationship filter for event-related models
 */
const addEventRelationFilter = (where: any, appId: string, model: string): any => {
  if (!where) where = {};

  try {
    if (isEventRelatedDirectModel(model)) {
      return {
        ...where,
        event: {
          ...where.event,
          appId,
        },
      };
    }

    if (isEventRelatedThroughFormModel(model)) {
      return {
        ...where,
        form: {
          ...where.form,
          event: {
            ...where.form?.event,
            appId,
          },
        },
      };
    }

    if (isEventRelatedThroughFormSubmissionModel(model)) {
      return {
        ...where,
        formSubmission: {
          ...where.formSubmission,
          form: {
            ...where.formSubmission?.form,
            event: {
              ...where.formSubmission?.form?.event,
              appId,
            },
          },
        },
      };
    }

    if (isEventRelatedThroughEventParticipantModel(model)) {
      // Validate model is in our configuration
      if (!validateModelName(model)) {
        throw new ModelConfigurationError(`Invalid model name: ${model}`, model);
      }

      const config = MODEL_RELATIONSHIP_CONFIG[model as PrismaModelName];
      if (!config) {
        throw new ModelConfigurationError(
          `No relationship configuration found for model: ${model}. Please add configuration to MODEL_RELATIONSHIP_CONFIG.`,
          model
        );
      }

      return {
        ...where,
        [config.relationName]: {
          ...where[config.relationName],
          event: {
            ...where[config.relationName]?.event,
            appId,
          },
        },
      };
    }

    return where;
  } catch (error) {
    console.error(`Error in addEventRelationFilter for model ${model}:`, error);
    throw error;
  }
};

/**
 * Validates if a record belongs to the current app for unique queries
 */
const validateRecordAccess = async (
  basePrisma: PrismaClient,
  result: any,
  args: any,
  model: string,
  currentAppId: string
): Promise<boolean> => {
  if (!result) return false;

  try {
    // Direct event-related models
    if (isEventRelatedDirectModel(model)) {
      const eventId =
        result.eventId ||
        args.where?.eventId ||
        args.where?.event_user_unique?.eventId ||
        args.where?.event?.id;

      if (!eventId) return false;

      const relatedEvent = await basePrisma.event.findUnique({
        where: { id: eventId },
        select: { appId: true },
      });

      return relatedEvent?.appId === currentAppId;
    }

    // Models related through Form
    if (isEventRelatedThroughFormModel(model)) {
      const formId = result.formId || args.where?.formId;
      if (!formId) return false;

      const relatedForm = await basePrisma.form.findUnique({
        where: { id: formId },
        select: { event: { select: { appId: true } } },
      });

      return relatedForm?.event?.appId === currentAppId;
    }

    // Models related through FormSubmission
    if (isEventRelatedThroughFormSubmissionModel(model)) {
      const formSubmissionId = result.formSubmissionId || args.where?.formSubmissionId;
      if (!formSubmissionId) return false;

      const relatedSubmission = await basePrisma.formSubmission.findUnique({
        where: { id: formSubmissionId },
        select: { form: { select: { event: { select: { appId: true } } } } },
      });

      return relatedSubmission?.form?.event?.appId === currentAppId;
    }

    // Models related through EventParticipant
    if (isEventRelatedThroughEventParticipantModel(model)) {
      const config = MODEL_RELATIONSHIP_CONFIG[model as PrismaModelName];
      if (!config) {
        throw new ModelConfigurationError(`No relationship configuration found for model: ${model}`, model);
      }

      const participantId = result[config.foreignKeyField] || args.where?.[config.foreignKeyField];
      if (!participantId) return false;

      const relatedParticipant = await basePrisma.eventParticipant.findUnique({
        where: { id: participantId },
        select: { event: { select: { appId: true } } },
      });

      return relatedParticipant?.event?.appId === currentAppId;
    }

    // Default deny for security
    return false;
  } catch (error) {
    console.error(`Error validating access for ${model}:`, error);
    return false;
  }
};

/**
 * Handles unique find operations with tenant validation
 */
const handleUniqueFind = async (
  basePrisma: PrismaClient,
  args: any,
  query: any,
  model: string,
  currentAppId: string
) => {
  if (isDirectlyFilteredModel(model)) {
    const result = await query(args);
    if (!result) return null;

    // Check if appId is in the result
    if (result.appId !== undefined) {
      return result.appId === currentAppId ? result : null;
    }

    // Manual verification using raw query to avoid extension recursion
    const recordWithAppId = await basePrisma.$queryRaw<Array<{ appId: string | null }>>`
      SELECT "appId" FROM "events" WHERE "id" = ${args.where.id}
    `;

    const recordAppId = recordWithAppId?.[0]?.appId;
    return recordAppId === currentAppId ? result : null;
  }

  // For event-related models
  if (isEventRelatedModel(model)) {
    const result = await query(args);
    const hasAccess = await validateRecordAccess(basePrisma, result, args, model, currentAppId);
    return hasAccess ? result : null;
  }

  return query(args);
};

/**
 * Creates the tenant filtering extension
 */
const createTenantFilteringExtension = (basePrisma: PrismaClient) => {
  return Prisma.defineExtension({
    name: "tenantFiltering",
    query: {
      $allModels: {
        async $allOperations({ model, operation, args, query }) {
          const currentAppId = getCurrentAppId();

          console.log(`Processing ${operation} on ${model} with appId: ${currentAppId}`);

          try {
            // Handle directly filtered models (Event)
            if (isDirectlyFilteredModel(model)) {
              switch (operation) {
                case "findMany":
                case "findFirst":
                case "count":
                  args.where = addAppIdFilter(args.where, currentAppId);
                  break;

                case "findUnique":
                case "findUniqueOrThrow":
                  return handleUniqueFind(basePrisma, args, query, model, currentAppId);

                case "create":
                  args.data = { ...args.data, appId: currentAppId };
                  break;

                case "update":
                case "updateMany":
                case "delete":
                case "deleteMany":
                  args.where = addAppIdFilter(args.where, currentAppId);
                  break;
              }
            }

            // Handle event-related models
            if (isEventRelatedModel(model)) {
              switch (operation) {
                case "findMany":
                case "findFirst":
                case "count":
                  args.where = addEventRelationFilter(args.where, currentAppId, model);
                  break;

                case "findUnique":
                case "findUniqueOrThrow":
                  return handleUniqueFind(basePrisma, args, query, model, currentAppId);
              }
            }

            return query(args);
          } catch (error) {
            console.error(`Error in tenant filtering extension for ${model}:`, error);
            throw new TenantFilterError(
              `Failed to apply tenant filtering: ${error instanceof Error ? error.message : "Unknown error"}`,
              model,
              operation
            );
          }
        },
      },
    },
  });
};

/**
 * Creates and configures the Prisma client with tenant filtering
 */
function createPrismaClient() {
  const basePrisma = new PrismaClient({
    log: process.env.NODE_ENV === "development" ? ["warn", "error"] : undefined,
    transactionOptions: { timeout: 30000 },
  });

  const tenantFilteringExtension = createTenantFilteringExtension(basePrisma);

  return basePrisma.$extends(tenantFilteringExtension);
}

// Initialize Prisma client with extensions
export const prisma = globalForPrisma.prisma || createPrismaClient();

// Set global instance in development
if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;
}

// Export types and utilities for external use
export type {
  DirectlyFilteredModel,
  EventRelatedModel,
  EventRelatedDirectModel,
  EventRelatedThroughFormModel,
  EventRelatedThroughFormSubmissionModel,
  EventRelatedThroughEventParticipantModel,
  PrismaModelName,
};

export {
  Prisma,
  MODEL_RELATIONSHIP_CONFIG,
  isDirectlyFilteredModel,
  isEventRelatedModel,
  isEventRelatedDirectModel,
  isEventRelatedThroughFormModel,
  isEventRelatedThroughFormSubmissionModel,
  isEventRelatedThroughEventParticipantModel,
  TenantFilterError,
  ModelConfigurationError,
};

// Utility function to validate model names at runtime using Prisma's enum
export const validateModelName = (model: string): model is PrismaModelName => {
  const validModels = Object.values(Prisma.ModelName);
  return validModels.includes(model as PrismaModelName);
};

// Utility function to get relationship config safely
export const getRelationshipConfig = (model: string): RelationshipConfig | null => {
  if (!validateModelName(model)) {
    console.log(`Invalid model name: ${model}`);
    return null;
  }

  return MODEL_RELATIONSHIP_CONFIG[model] || null;
};

// Validation function to ensure configuration stays in sync with schema
export const validateTenantConfiguration = (): {
  isValid: boolean;
  missingConfigurations: PrismaModelName[];
  invalidModels: string[];
} => {
  const allValidModels = Object.values(Prisma.ModelName);
  const configuredModels = new Set([
    ...TENANT_CONFIG.directlyFiltered,
    ...TENANT_CONFIG.eventRelated.direct,
    ...TENANT_CONFIG.eventRelated.nested.throughForm,
    ...TENANT_CONFIG.eventRelated.nested.throughFormSubmission,
    ...TENANT_CONFIG.eventRelated.nested.throughEventParticipant,
  ]);

  const invalidModels: string[] = [];
  const missingConfigurations: PrismaModelName[] = [];

  // Check if all configured models exist in Prisma schema
  configuredModels.forEach((model) => {
    if (!allValidModels.includes(model as PrismaModelName)) {
      invalidModels.push(model);
    }
  });

  // Check if models requiring relationship config have it
  TENANT_CONFIG.eventRelated.nested.throughEventParticipant.forEach((model) => {
    if (!MODEL_RELATIONSHIP_CONFIG[model]) {
      missingConfigurations.push(model);
    }
  });

  const isValid = invalidModels.length === 0 && missingConfigurations.length === 0;

  if (!isValid) {
    console.warn("Tenant configuration validation failed:", {
      invalidModels,
      missingConfigurations,
    });
  }

  return {
    isValid,
    missingConfigurations,
    invalidModels,
  };
};

// Run validation during module initialization (in development)
if (process.env.NODE_ENV !== "production") {
  validateTenantConfiguration();
}
