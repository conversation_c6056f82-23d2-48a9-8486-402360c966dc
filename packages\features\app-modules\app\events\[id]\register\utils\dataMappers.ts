import { BASIC_QUESTIONS } from "@/constants/form-questions";
import { EventWithQuestionsApiResponseData } from "../../../_api";
import { Question, QuestionType as FormQuestionType } from "../_components/question-info-form";

// Map API questions to component Question type
export const mapApiQuestionToComponentFormat = (
  apiQuestion: NonNullable<EventWithQuestionsApiResponseData["form"]>["questions"][number]
): Question => {
  // Parse options if they exist
  let options;
  if (apiQuestion.options) {
    try {
      if (typeof apiQuestion.options === "string") {
        options = JSON.parse(apiQuestion.options);
      } else {
        options = apiQuestion.options;
      }
    } catch (e) {
      options = [];
    }
  }

  // Map question type from API to component enum
  let questionType = FormQuestionType.TEXT;
  switch (apiQuestion.questionType) {
    case "RADIO":
      questionType = FormQuestionType.RADIO;
      break;
    case "CHECKBOX":
      questionType = FormQuestionType.CHECKBOX;
      break;
    default:
      questionType = FormQuestionType.TEXT;
  }

  // If it's a basic question, ensure the question text matches our constants
  const questionText = apiQuestion.isBasic
    ? BASIC_QUESTIONS[apiQuestion.questionText as keyof typeof BASIC_QUESTIONS] || apiQuestion.questionText
    : apiQuestion.questionText;

  return {
    id: apiQuestion.id,
    questionText: questionText || "Question",
    questionType,
    isRequired: apiQuestion.isRequired || false,
    options: Array.isArray(options) ? options : [],
    isPrivate: apiQuestion.isPrivate || false,
    order: apiQuestion.order || 0,
    isBasic: apiQuestion.isBasic,
  };
};

// Convert API ticket types to component format
export const mapApiTicketTypesToComponentFormat = (
  apiTicketTypes: EventWithQuestionsApiResponseData["ticketTypes"] | undefined
) => {
  const activeTickets = apiTicketTypes?.filter((ticketType: any) => ticketType.active === true);
  return activeTickets?.map((ticketType: any) => ({
    id: ticketType.id,
    name: ticketType.name,
    price:
      typeof ticketType.price === "string" ? parseFloat(ticketType.price) : Number(ticketType.price) || 0,
    description: ticketType.description || "No description available",
  }));
};
