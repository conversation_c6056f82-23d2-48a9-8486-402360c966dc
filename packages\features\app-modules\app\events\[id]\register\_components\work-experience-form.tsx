"use client";
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { useTranslations } from "next-intl";

import { GenericFormList, GenericFormConfig, FormField } from "./generic-form-list";
import { FormSchemaType, RegisterWorkExperienceJsonData } from "./types";

// Work experience specific configuration
const createWorkExperienceConfig = (t: any): GenericFormConfig<RegisterWorkExperienceJsonData> => ({
  getItemTitle: (item) => item.position,
  getItemSubtitle: (item) => item.company.name,
  getItemExtraInfo: (item) =>
    item.company.websiteUrl ? (
      <a
        href={item.company.websiteUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="text-xs text-blue-600 hover:underline">
        Website
      </a>
    ) : null,

  dialogTitle: {
    add: t("Add Work Experience"),
    edit: t("Edit Work Experience"),
  },

  getFormFields: (item, onChange): FormField[] => [
    {
      id: "position",
      label: t("Position or Title"),
      placeholder: "Product Designer",
      required: true,
      value: item.position,
      onChange: (value) => onChange({ position: value }),
    },
    {
      id: "company",
      label: t("Company Name"),
      placeholder: "MEEETUP Company",
      required: true,
      value: item.company.name,
      onChange: (value) => onChange({ company: { ...item.company, name: value } }),
    },
    {
      id: "website",
      label: t("Company Website (optional)"),
      placeholder: "https://example.com",
      type: "url" as const,
      value: item.company.websiteUrl || "",
      onChange: (value) => onChange({ company: { ...item.company, websiteUrl: value } }),
    },
  ],

  isValid: (item) => !!item.position && !!item.company.name && !!item.startDate,

  currentStatusConfig: {
    checkboxLabel: t("I currently work here"),
    endDateLabel: t("End Date"),
  },

  emptyStateMessage: t("No work experience added yet"),
  addButtonText: t("Add work experience"),
  requiredErrorMessage: t("At least one work experience entry is required"),
});

interface WorkExperienceFormProps {
  formField: string;
  form: UseFormReturn<FormSchemaType>;
  label: string;
  required?: boolean;
}

export function WorkExperienceForm({ formField, form, label, required = false }: WorkExperienceFormProps) {
  const t = useTranslations("EventRegistration");

  const createNewWorkExperience = (): RegisterWorkExperienceJsonData => ({
    id: "",
    position: "",
    company: { name: "" },
    startDate: new Date().toISOString(),
  });

  const config = createWorkExperienceConfig(t);

  return (
    <GenericFormList
      formField={formField}
      form={form}
      label={label}
      required={required}
      config={config}
      createNewItem={createNewWorkExperience}
    />
  );
}
