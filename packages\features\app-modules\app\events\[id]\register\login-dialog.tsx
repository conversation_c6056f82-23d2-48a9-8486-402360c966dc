"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { UserCircle } from "lucide-react";
import { useTranslations } from "next-intl";

import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@meeeetup/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@meeeetup/ui/tabs";
import { DialogLoginForm } from "./dialog-login-form";
import { getSignupUrl } from "@/hooks/use-auth-handlers";
import { DialogSignupForm } from "./dialog-signup-form";

export function LoginDialog({ eventId }: { eventId: string }) {
  const [open, setOpen] = useState(true);
  const router = useRouter();
  const t = useTranslations("LoginDialog");

  const currentPath = `/events/${eventId}/register`;
  const signupUrl = getSignupUrl(currentPath);

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      router.back();
    }
  };

  const handleLoginSuccess = () => {
    setOpen(false);
  };

  const handleSignupSuccess = () => {
    setOpen(false);
    // Potentially redirect or show a success message, then close or redirect.
    // For now, just closing.
  };

  return (
    <div className="p-6">
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent
          className="sm:max-w-[500px] w-full max-h-[90vh] flex flex-col overflow-auto"
          onInteractOutside={(e) => {
            e.preventDefault();
          }}>
          <div className="text-center mb-4 flex-shrink-0">
            <div className="flex justify-center mb-4">
              <div className="p-4 rounded-full bg-blue-50">
                <UserCircle className="w-16 h-16 text-blue-500" />
              </div>
            </div>
            <DialogTitle className="text-mu-base text-h3-bold">{t("Login Required")}</DialogTitle>
            <p className="text-body1 text-gray-700 mt-2">
              {t("You need to be logged in to register for this event")}
            </p>
          </div>

          <div className="px-6 pb-4 flex-1 min-h-0 flex flex-col">
            <Tabs defaultValue="signup" className="w-full flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-2 mb-6 rounded-full p-1 border border-gray-100 shadow-sm flex-shrink-0">
                <TabsTrigger
                  value="login"
                  className="rounded-full py-2.5 transition-all duration-300 data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md font-medium">
                  {t("Log in")}
                </TabsTrigger>
                <TabsTrigger
                  value="signup"
                  className="rounded-full py-2.5 transition-all duration-300 data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md font-medium">
                  {t("Sign up")}
                </TabsTrigger>
              </TabsList>
              <div className="flex-1 min-h-0 overflow-y-auto pb-4">
                <TabsContent
                  value="login"
                  className="mt-4 animate-in fade-in-50 slide-in-from-left-3 duration-300 h-full">
                  <DialogLoginForm
                    nextUrl={currentPath}
                    signupUrl={signupUrl}
                    onLoginSuccess={handleLoginSuccess}
                  />
                </TabsContent>
                <TabsContent
                  value="signup"
                  className="mt-4 animate-in fade-in-50 slide-in-from-right-3 duration-300 h-full">
                  <DialogSignupForm nextUrl={currentPath} onSignupSuccess={handleSignupSuccess} />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
