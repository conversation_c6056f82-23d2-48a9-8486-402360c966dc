import { useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";

import { getPublicEventWithQuestionsApi } from "../../../_api"; // Path relative to this hook file

export function useEventData() {
  const params = useParams();
  const eventId = params.id as string;

  const {
    data: eventResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["event", eventId],
    queryFn: () => getPublicEventWithQuestionsApi(eventId),
    enabled: !!eventId,
    staleTime: 0, // Consider making this configurable or removing if not needed
  });

  const event = eventResponse?.success ? eventResponse.data : null;

  return {
    event,
    isLoading,
    error,
    eventResponse, // Exporting the raw response might also be useful
  };
}
