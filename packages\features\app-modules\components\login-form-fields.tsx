"use client";

import { <PERSON><PERSON><PERSON><PERSON>, UseFormHandleSubmit, UseFormRegister } from "react-hook-form";
import Link from "next/link";
import { z } from "zod";
import { useTranslations } from "next-intl";

import { Input, PasswordInput, Button, Checkbox } from "@meeeetup/ui";
import GoogleButton from "@/app/(auth)/_components/oauth-buttons/google";
import { loginSchema } from "@/schema/auth/login"; // Import the schema

// Use the schema to infer the form data type
export type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormFieldsProps {
  loginType: "organizer" | "participant";
  handleGoogleLogin: (loginType: "organizer" | "participant") => Promise<void>;
  onSubmit: (data: LoginFormData) => void;
  register: UseFormRegister<LoginFormData>;
  handleSubmit: UseFormHandleSubmit<LoginFormData>;
  errors: FieldErrors<LoginFormData>;
  isPending: boolean;
  googleLoading: boolean;
  t: ReturnType<typeof useTranslations>;
  signupUrl: string;
}

export function LoginFormFields({
  loginType,
  handleGoogleLogin,
  onSubmit,
  register,
  handleSubmit,
  errors,
  isPending,
  googleLoading,
  t,
  signupUrl,
}: LoginFormFieldsProps) {
  return (
    <>
      <GoogleButton
        onClick={async () => {
          await handleGoogleLogin(loginType);
        }}
        loading={googleLoading}
      />

      <div className="flex items-center my-6">
        <div className="flex-grow h-px bg-gray-200"></div>
        <span className="mx-4 text-sm text-gray-500">{t("orContinueWith")}</span>
        <div className="flex-grow h-px bg-gray-200"></div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            {t("email")}
          </label>
          <Input
            placeholder={t("emailPlaceholder")}
            id="email"
            type="email"
            className="w-full"
            {...register("email", {
              required: t("emailRequired"),
            })}
            aria-invalid={errors.email ? "true" : "false"}
          />
          {errors.email && (
            <p className="text-sm text-red-500" role="alert">
              {errors.email.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            {t("password")}
          </label>
          <PasswordInput
            placeholder={t("passwordPlaceholder")}
            id="password"
            className="w-full"
            {...register("password", { required: t("passwordRequired") })}
            aria-invalid={errors.password ? "true" : "false"}
          />
          {errors.password && (
            <p className="text-sm text-red-500" role="alert">
              {errors.password.message}
            </p>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Checkbox id="remember-me" variant="secondary" size="medium" />
            <label htmlFor="remember-me" className="text-sm text-gray-600">
              {t("rememberMe")}
            </label>
          </div>
          <Link href="/forgot-password" className="text-sm text-mu-primary hover:text-mu-primary-dark">
            {t("forgotPassword")}
          </Link>
        </div>

        <Button type="submit" variant="primary" size="large" className="w-full mt-6" disabled={isPending}>
          {isPending ? t("loggingIn") : t("loginButton")}
        </Button>
      </form>

      <div className="text-center mt-8 text-sm text-gray-600">
        {t("noAccount")}{" "}
        <Link href={signupUrl} className="font-medium text-mu-primary hover:text-mu-primary-dark">
          {t("register")}
        </Link>
      </div>
    </>
  );
}
