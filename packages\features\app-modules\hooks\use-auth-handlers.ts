import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@meeeetup/ui/toaster";
import { signIn } from "next-auth/react";
import { setAuthRole } from "../app/(auth)/_actions/signup-action";

export const getSignupUrl = (nextUrlOverride?: string): string => {
  const searchParams = new URLSearchParams(typeof window !== "undefined" ? window.location.search : "");
  const nextParam = nextUrlOverride || searchParams.get("next");

  if (nextParam) {
    return `/signup?next=${encodeURIComponent(nextParam)}`;
  }
  return "/signup";
};

export const useAuthHandlers = () => {
  const { toast } = useToast();
  const router = useRouter();

  const handleLoginSuccess = useCallback(
    (nextUrlOverride?: string) => {
      const searchParams = new URLSearchParams(typeof window !== "undefined" ? window.location.search : "");
      const nextUrl = nextUrlOverride || searchParams.get("next");

      if (nextUrl) {
        router.push(nextUrl);
      } else {
        router.push("/autheticating");
      }
    },
    [router]
  );

  const handleLoginError = useCallback(
    (error?: string) => {
      toast({
        title: "Login Failed",
        description: error || "Invalid email or password",
        variant: "destructive",
      });
    },
    [toast]
  );

  return { handleLoginSuccess, handleLoginError };
};

export const useSocialLogin = () => {
  const handleGoogleLogin = useCallback(
    async (loginType: "organizer" | "participant", nextUrlOverride?: string) => {
      await setAuthRole(loginType === "organizer" ? "host" : "participant");

      const searchParams = new URLSearchParams(typeof window !== "undefined" ? window.location.search : "");
      const nextUrl = nextUrlOverride || searchParams.get("next");
      let callbackUrl;
      if (nextUrl) {
        callbackUrl = "/autheticating?next=" + encodeURIComponent(nextUrl);
      } else {
        callbackUrl = "/autheticating";
      }

      console.log("Social login callbackUrl:", callbackUrl);

      try {
        await signIn("google", { callbackUrl });
      } catch (error) {
        console.error("Error handling Google login:", error);
        // Optionally, add a toast notification for Google login failure here
        // For example: toast({ title: "Google Login Failed", description: "Could not sign in with Google.", variant: "destructive" });
        await signIn("google", { callbackUrl }); // Retry or handle error appropriately
      }
    },
    []
  );

  return { handleGoogleLogin };
};
