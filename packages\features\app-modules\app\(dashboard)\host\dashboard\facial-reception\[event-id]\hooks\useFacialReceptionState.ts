"use client";

import { useReducer } from "react";
import { PotentialMatchUser } from "../types";
import { QRCodeDialogData } from "./useFacialReceptionManager";

export type FacialReceptionStatus =
  | "IDLE"
  | "DETECTING"
  | "FACE_NOT_FOUND"
  | "MATCHES_FOUND"
  | "CONFIRMING"
  | "SEARCHING"
  | "QR_CODE_DISPLAY"
  | "REGISTRATION"
  | "WAITING_PERMISSION";

export interface FacialReceptionState {
  status: FacialReceptionStatus;
  capturedImage: string | null;
  potentialMatches: PotentialMatchUser[] | null;
  currentMatchIndex: number;
  qrCodeDialogData: QRCodeDialogData | null;
  error: string | null;
  isManualSelection: boolean;
}

export type FacialReceptionAction =
  | { type: "CAPTURE_IMAGE"; payload: string }
  | { type: "DETECTION_SUCCESS"; payload: PotentialMatchUser[] }
  | { type: "MANUAL_MATCH_SELECTED"; payload: PotentialMatchUser }
  | { type: "DETECTION_FAILURE"; payload?: { error: string } }
  | { type: "CONFIRM_SELECTION" }
  | { type: "CONFIRMATION_SUCCESS" }
  | { type: "CONFIRMATION_FAILURE"; payload: { error: string } }
  | { type: "SHOW_NEXT_MATCH" }
  | { type: "SHOW_PREV_MATCH" }
  | { type: "START_SEARCH" }
  | { type: "CLOSE_SEARCH" }
  | { type: "SHOW_QR_CODE"; payload: QRCodeDialogData }
  | { type: "CLOSE_QR_CODE" }
  | { type: "START_REGISTRATION" }
  | { type: "RESET" };

const initialState: FacialReceptionState = {
  status: "IDLE",
  capturedImage: null,
  potentialMatches: null,
  currentMatchIndex: 0,
  qrCodeDialogData: null,
  error: null,
  isManualSelection: false,
};

function facialReceptionReducer(
  state: FacialReceptionState,
  action: FacialReceptionAction
): FacialReceptionState {
  switch (action.type) {
    case "CAPTURE_IMAGE":
      return {
        ...initialState,
        status: "DETECTING",
        capturedImage: action.payload,
      };
    case "DETECTION_SUCCESS":
      return {
        ...state,
        status: "MATCHES_FOUND",
        potentialMatches: action.payload,
        currentMatchIndex: 0,
        error: null,
        isManualSelection: false,
      };
    case "MANUAL_MATCH_SELECTED":
      return {
        ...state,
        status: "MATCHES_FOUND",
        potentialMatches: [action.payload],
        currentMatchIndex: 0,
        error: null,
        isManualSelection: true,
      };
    case "DETECTION_FAILURE":
      return {
        ...state,
        status: "FACE_NOT_FOUND",
        potentialMatches: null,
        error: action.payload?.error || "Face not found or no matches.",
      };
    case "CONFIRM_SELECTION":
      return { ...state, status: "CONFIRMING", error: null };
    case "CONFIRMATION_SUCCESS":
      return { ...initialState, status: "WAITING_PERMISSION" };
    case "CONFIRMATION_FAILURE":
      return {
        ...state,
        status: "MATCHES_FOUND", // Return to matches view
        error: action.payload.error,
      };
    case "SHOW_NEXT_MATCH":
      if (!state.potentialMatches) return state;
      return {
        ...state,
        currentMatchIndex: (state.currentMatchIndex + 1) % state.potentialMatches.length,
      };
    case "SHOW_PREV_MATCH":
      if (!state.potentialMatches) return state;
      return {
        ...state,
        currentMatchIndex:
          (state.currentMatchIndex - 1 + state.potentialMatches.length) % state.potentialMatches.length,
      };
    case "START_SEARCH":
      return { ...initialState, status: "SEARCHING" };
    case "CLOSE_SEARCH":
      return { ...initialState };
    case "SHOW_QR_CODE":
      return {
        ...state,
        status: "QR_CODE_DISPLAY",
        qrCodeDialogData: action.payload,
      };
    case "CLOSE_QR_CODE":
      return { ...initialState, status: "WAITING_PERMISSION" };
    case "START_REGISTRATION":
      return { ...state, status: "REGISTRATION" };
    case "RESET":
      return { ...initialState };
    default:
      return state;
  }
}

export function useFacialReceptionState() {
  const [state, dispatch] = useReducer(facialReceptionReducer, initialState);

  const { potentialMatches, currentMatchIndex } = state;

  const currentUser = potentialMatches && potentialMatches[currentMatchIndex];

  return {
    state,
    dispatch,
    currentUser,
  };
}
