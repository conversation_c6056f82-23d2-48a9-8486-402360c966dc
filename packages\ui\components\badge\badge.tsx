import * as React from "react";

interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: string; // Or a more specific type if variants are known
  className?: string;
}

export const Badge: React.FC<BadgeProps> = ({ children, variant, className, ...props }) => {
  // Basic implementation, can be styled further based on variant or specific needs
  const baseClasses =
    "inline-flex items-center rounded-md border px-4 py-1 text-sm font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2";
  // Example of handling variant, customize as needed
  const variantClasses =
    variant === "secondary"
      ? "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"
      : "";

  return (
    <div className={`${baseClasses} ${variantClasses} ${className || ""}`} {...props}>
      {children}
    </div>
  );
};
