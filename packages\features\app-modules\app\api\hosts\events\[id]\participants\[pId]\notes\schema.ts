import { z } from "zod";

export const noteAddSchema = z.object({
  participantId: z.string(),
  eventId: z.string(),
  noteText: z.string(),
});

export const noteUpdateSchema = z.object({
  participantId: z.string(),
  eventId: z.string(),
  noteId: z.string(),
  noteText: z.string(),
});

export type AddNoteType = z.infer<typeof noteAddSchema>;
export type UpdateNoteType = z.infer<typeof noteUpdateSchema>;
