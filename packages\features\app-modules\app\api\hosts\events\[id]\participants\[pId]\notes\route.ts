import { NextRequest } from "next/server";

import { errors } from "@/lib/errors";
import { responseJson } from "@/lib/response";
import { withHostRole } from "@/middleware-helpers/with-host-role";
import {
  deleteEventParticipant,
  getAllEventsByHostAndParticipant,
  getEventParticipantDetails,
  manualEventParticipantCheckInOut,
} from "@/services/event-participants.service";
import { prisma } from "@meeeetup/prisma";
import { zodParse } from "@/lib/zod";
import { noteAddSchema, noteUpdateSchema } from "./schema";

// /api/hosts/events/[id]/participants/[pId]/notes
/* body
{
  participantId?: string;
  eventId?: string;
  noteText?: string;
}
*/
async function POST(request: NextRequest, { params }: { params: Promise<{ id: string; pId: string }> }) {
  let session;

  try {
    session = await withHostRole();
    const hostId = session.user.id;

    const body = await request.json();
    const { eventId, noteText, participantId } = await zodParse(noteAddSchema, body);

    if (!eventId || !participantId) {
      throw errors.badRequest("Missing eventId or participantId in path parameters.");
    }

    const note = await prisma.note.create({
      data: {
        eventParticipantId: participantId,
        writtenBy: hostId,
        noteText: noteText,
        type: "HOST_TO_PARTICIPANT",
        active: true,
      },
    });

    if (!note) {
      throw errors.internal("Failed to create note");
    }

    return responseJson.success({
      data: note,
      message: "Event participant updated successfully!",
      logContext: { session },
    });
  } catch (error) {
    console.error("Error in PUT handler for participant:", error);
    return responseJson.error({
      error,
      message: "Error updating event participant or adding note!",
      logContext: { session },
    });
  }
}

// /api/hosts/events/[id]/participants/[pId]/notes
/* body
{
  participantId: string;
  eventId: string;
  noteId: string;
  noteText: string;
}
*/
async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string; pId: string }> }) {
  let session;

  try {
    session = await withHostRole();
    const hostId = session.user.id;

    const body = await request.json();
    const { eventId, participantId, noteId, noteText } = await zodParse(noteUpdateSchema, body);

    const updatedNote = await prisma.note.update({
      where: {
        id: noteId,
        eventParticipantId: participantId,
      },
      data: {
        noteText: noteText,
      },
    });

    if (!updatedNote) {
      throw errors.notFound("Note not found or does not belong to this participant/event.");
    }

    return responseJson.success({
      data: updatedNote,
      message: "Event participant note updated successfully!",
      logContext: { session, noteId, participantId, eventId },
    });
  } catch (error: any) {
    console.error("Error in PUT handler for participant note:", error);

    return responseJson.error({
      error,
      message: "Error updating event participant note!",
      logContext: { session },
    });
  }
}

async function GET(request: NextRequest, { params }: { params: Promise<{ id: string; pId: string }> }) {
  let session;

  try {
    session = await withHostRole();
    const hostId = session.user.id;

    const { pId, id } = await params;

    if (!pId) {
      throw errors.badRequest("Missing participantId in path parameters.");
    }

    const admins = await prisma.event.findMany({
      where: {
        id,
      },
      select: {
        admins: {
          select: {
            hostId: true,
            addedBy: true,
          },
        },
      },
    });

    const adminList = Array.from(
      new Set(admins.flatMap((event) => event.admins.flatMap((admin) => [admin.hostId, admin.addedBy])))
    );

    const notes = await prisma.note.findMany({
      where: {
        eventParticipantId: pId,
        OR: [
          {
            writtenBy: hostId,
          },
          {
            writtenBy: {
              in: adminList,
            },
          },
        ],
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    const noteResponse = notes.map((n) => {
      return {
        ...n,
        writtenBy: n.writtenBy === hostId ? "You" : "Other Admins",
      };
    });

    return responseJson.success({
      data: noteResponse,
      message: "Notes fetched successfully!",
      logContext: { session, pId },
    });
  } catch (error: any) {
    console.error("Error in GET handler for participant notes:", error);

    return responseJson.error({
      error,
      message: "Error fetching participant notes!",
      logContext: { session },
    });
  }
}

export { POST, PUT, GET };
