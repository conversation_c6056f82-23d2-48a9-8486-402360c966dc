"use client";
import React, { useState, useEffect, useCallback } from "react";
import { GoogleMap, useJs<PERSON><PERSON><PERSON>oa<PERSON>, <PERSON><PERSON> } from "@react-google-maps/api";
import {
  GOOGLE_MAPS_LIBRARIES,
  DEFAULT_CONTAINER_STYLE,
  DEFAULT_MAP_CENTER,
  DEFAULT_ZOOM_LEVEL,
  SELECTED_LOCATION_ZOOM_LEVEL,
} from "./map-constants";

interface MapClientComponentProps {
  searchQuery: string;
  height?: string;
  selectedCoordinates?: [number, number] | null;
}

const API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "AIzaSyDTXc9vkpSRWTgC9dKR1CDBENlJKv82ph0"; // Keep your fallback or handle missing key

export function MapClientComponent({
  searchQuery,
  height = DEFAULT_CONTAINER_STYLE.height,
  selectedCoordinates = null,
}: MapClientComponentProps) {
  console.log("[MapClientComponent] Initializing with API Key:", API_KEY ? "Provided" : "Fallback/Missing");
  const { isLoaded, loadError } = useJsApiLoader({
    id: "google-map-script", // This ID should be consistent if used elsewhere
    googleMapsApiKey: API_KEY,
    libraries: GOOGLE_MAPS_LIBRARIES,
  });

  useEffect(() => {
    console.log("[MapClientComponent] isLoaded state:", isLoaded);
  }, [isLoaded]);

  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [marker, setMarker] = useState<google.maps.LatLngLiteral | null>(null);

  const containerStyle = {
    ...DEFAULT_CONTAINER_STYLE,
    height,
  };

  const onLoad = useCallback((loadedMap: google.maps.Map) => {
    console.log("[MapClientComponent] GoogleMap onLoad triggered, map instance:", loadedMap);
    setMap(loadedMap);
  }, []);

  const onUnmount = useCallback(() => {
    setMap(null);
  }, []);

  useEffect(() => {
    if (map && selectedCoordinates) {
      const [lat, lng] = selectedCoordinates;
      if (typeof lat === "number" && typeof lng === "number" && !isNaN(lat) && !isNaN(lng)) {
        const location = { lat, lng };
        setMarker(location);
        map.setCenter(location);
        map.setZoom(SELECTED_LOCATION_ZOOM_LEVEL);
        console.log("Map centered to selected coordinates:", location);
      } else {
        console.warn("Invalid selected coordinates received:", selectedCoordinates);
      }
    }
  }, [selectedCoordinates, map]);

  const searchPlaces = useCallback(
    async (query: string) => {
      if (!isLoaded || !map || !query) return;

      try {
        const request = {
          textQuery: query,
          fields: ["displayName", "location", "formattedAddress"], // Added formattedAddress
          // Consider making locationBias more dynamic or configurable if needed
          locationBias: map.getCenter()
            ? {
                lat: map.getCenter()!.lat(),
                lng: map.getCenter()!.lng(),
              }
            : undefined, // Or a default bias
        };

        const { places } = await google.maps.places.Place.searchByText(request);

        if (places && places.length > 0) {
          const place = places[0];
          if (place?.location) {
            const lat =
              typeof place.location.lat === "function" ? place.location.lat() : Number(place.location.lat);
            const lng =
              typeof place.location.lng === "function" ? place.location.lng() : Number(place.location.lng);

            if (typeof lat === "number" && typeof lng === "number" && !isNaN(lat) && !isNaN(lng)) {
              const location = { lat, lng };
              setMarker(location);
              map.setCenter(location);
              map.setZoom(SELECTED_LOCATION_ZOOM_LEVEL); // Consistent zoom after search
              console.log("Found place:", place.displayName, "at", location);
            } else {
              console.error("Invalid coordinates from search result:", { lat, lng }, place);
            }
          } else {
            console.warn("Search result found, but no location data:", place);
          }
        } else {
          console.log("No places found for query:", query);
          // Optionally clear marker or show a message
          // setMarker(null);
        }
      } catch (error) {
        console.error("Error searching for places:", error);
      }
    },
    [isLoaded, map] // Removed GOOGLE_MAPS_LIBRARIES as it's stable
  );

  useEffect(() => {
    // Only search if there's a query and no specific coordinates are already selected
    if (searchQuery && !selectedCoordinates) {
      const timeoutId = setTimeout(() => {
        searchPlaces(searchQuery);
      }, 500); // Debounce search

      return () => clearTimeout(timeoutId);
    }
  }, [searchQuery, searchPlaces, selectedCoordinates]);

  if (loadError) {
    console.error("[MapClientComponent] useJsApiLoader loadError:", loadError);
    return (
      <div
        className="flex items-center justify-center w-full bg-red-100 border border-red-400 text-red-700 rounded-md"
        style={{ height }}>
        <div className="text-center p-4">
          <p>Error loading Google Maps.</p>
          <p>Please check your API key and network connection.</p>
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    console.log("[MapClientComponent] Rendering 'Loading Maps...' because isLoaded is false.");
    return (
      <div className="flex items-center justify-center w-full bg-gray-100 rounded-md" style={{ height }}>
        <div className="text-gray-500 text-center p-4">Loading Maps...</div>
      </div>
    );
  }

  console.log("[MapClientComponent] Rendering GoogleMap component.");
  return (
    // Consider if zIndex: 0 is strictly necessary here.
    // It can sometimes affect stacking contexts in unexpected ways.
    <div className="relative isolate" style={{ zIndex: 0 }}>
      <GoogleMap
        mapContainerStyle={containerStyle}
        center={marker || DEFAULT_MAP_CENTER} // Center on marker if available, else default
        zoom={DEFAULT_ZOOM_LEVEL}
        onLoad={onLoad}
        onUnmount={onUnmount}
        options={{
          disableDefaultUI: true,
          scrollwheel: true, // Standard behavior
          zoomControl: true,
          streetViewControl: false, // Typically not needed for this kind of map
          mapTypeControl: false, // Simplifies UI
          fullscreenControl: false, // Optional, can be enabled if useful
        }}>
        {marker && <Marker position={marker} />}
      </GoogleMap>
    </div>
  );
}

export default MapClientComponent;
