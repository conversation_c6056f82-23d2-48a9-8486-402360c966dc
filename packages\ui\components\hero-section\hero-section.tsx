import { ReactNode } from "react";
import { Search, Sparkles } from "lucide-react";
import { SearchBar } from "@meeeetup/ui/search-bar";

interface HeroSectionProps {
  title: string;
  subtitle: string;
  searchPlaceholder: string;
  onSearch?: (value: string) => void;
  children?: ReactNode;
}

export function HeroSection({ title, subtitle, searchPlaceholder, onSearch, children }: HeroSectionProps) {
  return (
    <section className="relative overflow-hidden bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-700">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400/30 via-blue-600/20 to-indigo-700/10"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-400/20 via-transparent to-transparent"></div>

      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
        <div className="absolute top-10 left-10 w-72 h-72 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-96 h-96 bg-indigo-300/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-purple-300/15 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>

      <div className="relative container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-24 py-16 md:py-24">
        <div className="text-center">
          <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6 text-white/90 text-sm font-medium">
            <Sparkles className="w-4 h-4" />
            Discover Amazing Events
          </div>

          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight">
            {title}
          </h1>

          <p className="text-xl md:text-2xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            {subtitle}
          </p>

          {/* Enhanced Search Bar */}
          <div className="max-w-2xl mx-auto">
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-white to-blue-50 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-1000"></div>
              <div className="relative bg-white/95 backdrop-blur-sm rounded-2xl p-2 shadow-2xl">
                <div className="flex items-center gap-3">
                  <Search className="w-6 h-6 text-gray-400 ml-4" />
                  <SearchBar
                    className="border-0 outline-none focus-visible:ring-0"
                    hideSearchIcon
                    placeholder={searchPlaceholder}
                    onSearch={(value) => onSearch?.(value)}
                  />
                  <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                    Search
                  </button>
                </div>
              </div>
            </div>
          </div>

          {children}
        </div>
      </div>
    </section>
  );
}
