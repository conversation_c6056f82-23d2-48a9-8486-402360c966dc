"use client";
import { CalendarIcon, EditIcon, LoaderIcon, SaveIcon, UserIcon, XCircleIcon } from "lucide-react";
import { formatDistanceToNowStrict } from "date-fns";
import { GetNoteApiResponseData } from "../../_api";
import { useState } from "react";
import { Textarea } from "@meeeetup/ui";

interface NoteCardProps {
  note: GetNoteApiResponseData;
  isSavingNote: boolean;
  updateNoteError: string | null;
  onSave: (noteId: string, newText: string) => Promise<void>;
  onEdit: (note: GetNoteApiResponseData) => void;
  onCancelEdit: () => void;
  isEditingThisNote: boolean;
}

export const NoteCard = ({
  note,
  isSavingNote,
  updateNoteError,
  onSave,
  onEdit,
  onCancelEdit,
  isEditingThisNote,
}: NoteCardProps) => {
  const [editingNoteText, setEditingNoteText] = useState(note.noteText || "");

  const createdAt = new Date(note.createdAt);
  const updatedAt = note.updatedAt ? new Date(note.updatedAt) : null;
  const isEdited = updatedAt && updatedAt.getTime() !== createdAt.getTime();

  const handleSave = () => {
    onSave(note.id, editingNoteText);
  };

  return (
    <div
      className={`bg-white rounded-xl border border-gray-200 p-4 md:p-6 shadow-sm hover:shadow-md transition-all duration-200 ${
        isEditingThisNote ? "ring-2 ring-blue-500 ring-opacity-20" : ""
      }`}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div className="flex items-center space-x-3">
          <div className="w-full">
            <div className="flex items-center text-sm text-gray-700 font-medium">
              <UserIcon className="min-w-4 h-4 mr-1" />
              <span className="truncate">{note.writtenBy}</span>
            </div>
            <div className="flex flex-wrap items-center text-xs text-gray-500 mt-1">
              <CalendarIcon className="min-w-3 h-3 mr-1" />
              <span>{formatDistanceToNowStrict(createdAt, { addSuffix: true })}</span>
              {isEdited && (
                <span className="ml-2 text-amber-600 font-medium">
                  • Edited {formatDistanceToNowStrict(updatedAt!, { addSuffix: true })}
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          {isEditingThisNote ? (
            <>
              <button
                onClick={handleSave}
                disabled={isSavingNote || !editingNoteText.trim()}
                className="px-3 py-1.5 sm:px-4 sm:py-2 bg-lime-50 hover:bg-lime-100 text-lime-700 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-1.5">
                {isSavingNote ? (
                  <LoaderIcon className="w-4 h-4 animate-spin" />
                ) : (
                  <SaveIcon className="w-4 h-4" />
                )}
                <span>Save</span>
              </button>
              <button
                onClick={onCancelEdit}
                disabled={isSavingNote}
                className="px-3 py-1.5 sm:px-4 sm:py-2 bg-amber-50 hover:bg-amber-100 text-amber-700 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-1.5">
                <XCircleIcon className="w-4 h-4" />
                <span>Cancel</span>
              </button>
            </>
          ) : (
            <button
              onClick={() => onEdit(note)}
              className="px-3 py-1.5 sm:px-4 sm:py-2 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-1.5">
              <EditIcon className="w-4 h-4" />
              <span>Edit</span>
            </button>
          )}
        </div>
      </div>

      {updateNoteError && isEditingThisNote && (
        <p className="text-red-500 text-sm mt-2 mb-2">{updateNoteError}</p>
      )}

      <div className="mt-4">
        {isEditingThisNote ? (
          <Textarea
            value={editingNoteText}
            onChange={(e) => setEditingNoteText(e.target.value)}
            className="w-full"
            rows={4}
            placeholder="Write your note here..."
          />
        ) : (
          <div className="text-gray-800 text-sm leading-relaxed whitespace-pre-wrap bg-gray-50 p-3 md:p-4 rounded-lg border-l-4 border-blue-500">
            {note.noteText}
          </div>
        )}
      </div>
    </div>
  );
};
