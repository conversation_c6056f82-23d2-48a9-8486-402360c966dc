import React from "react";
import { Button } from "@meeeetup/ui/button";

interface ErrorStateProps {
  title: string;
  message: string;
  buttonText?: string;
  onButtonClick?: () => void;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title,
  message,
  buttonText = "Go Back",
  onButtonClick = () => window.history.back(),
}) => {
  return (
    <div className="w-full mx-auto max-w-4xl p-4 sm:px-[100px] space-y-8">
      <h1 className="text-2xl font-bold mb-2 text-center">{title}</h1>
      <p className="text-center text-red-500">{message}</p>
      <Button className="mx-auto block" onClick={onButtonClick}>
        {buttonText}
      </Button>
    </div>
  );
};

export const EventNotFoundError: React.FC = () => (
  <ErrorState title="Event Not Found" message="The event you're trying to register for could not be found." />
);

export const EventLoadingError: React.FC<{ error?: boolean }> = ({ error }) => (
  <ErrorState
    title="Error Loading Event"
    message={error ? "An error occurred while loading the event." : "Event not found or failed to load."}
  />
);
