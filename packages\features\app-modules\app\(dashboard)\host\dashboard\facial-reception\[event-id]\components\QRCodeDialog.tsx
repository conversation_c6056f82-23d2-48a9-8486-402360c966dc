"use client";

import { <PERSON><PERSON>, DialogContent, Di<PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@meeeetup/ui/dialog";
import { QRCode } from "@meeeetup/ui/qrcode";
import { Button } from "@meeeetup/ui/button";
import { useTranslations } from "next-intl";

interface QRCodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userName?: string;
  qrCodeValue: string;
  onContinue: () => void;
  eventDashboardUrl: string; // Added to ensure QRCode always has a fallback value
}

export function QRCodeDialog({
  open,
  onOpenChange,
  userName,
  qrCodeValue,
  onContinue,
  eventDashboardUrl,
}: QRCodeDialogProps) {
  const t = useTranslations("FacialReception.QRCodeDialog");
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-semibold">{t("title")}</DialogTitle>
          {userName && (
            <DialogDescription className="text-center">{t("successMessage", { userName })}</DialogDescription>
          )}
        </DialogHeader>
        <div className="flex flex-col items-center justify-center py-4">
          <QRCode
            title={t("qrCodeTitle")}
            subtitle={t("qrCodeSubtitle")}
            value={qrCodeValue || eventDashboardUrl} // Use qrCodeValue if available, else fallback
            size={180} // Slightly smaller for mobile
          />
          <div className="mt-6 mb-2 text-center">
            <Button onClick={onContinue} className="px-6">
              {t("continueButton")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
