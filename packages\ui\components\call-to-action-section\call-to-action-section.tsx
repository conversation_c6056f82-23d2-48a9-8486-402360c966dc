import Link from "next/link";
import { ArrowRight } from "lucide-react";

interface CallToActionSectionProps {
  title: string;
  subtitle: string;
  primaryButtonText: string;
  primaryButtonHref: string;
  secondaryButtonText: string;
  secondaryButtonHref: string;
  className?: string;
}

export function CallToActionSection({
  title,
  subtitle,
  primaryButtonText,
  primaryButtonHref,
  secondaryButtonText,
  secondaryButtonHref,
  className = "",
}: CallToActionSectionProps) {
  return (
    <section
      className={`p-4 relative overflow-hidden bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-700 rounded-3xl ${className}`}>
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
        <div className="absolute top-10 right-10 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-80 h-80 bg-pink-300/20 rounded-full blur-3xl"></div>
      </div>

      <div className="relative px-8 py-16 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{title}</h2>
        <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">{subtitle}</p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href={primaryButtonHref}
            className="inline-flex items-center gap-2 bg-white text-blue-600 px-8 py-4 rounded-2xl font-semibold hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-xl">
            {primaryButtonText}
            <ArrowRight className="w-5 h-5" />
          </Link>
          <Link
            href={secondaryButtonHref}
            className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-2xl font-semibold hover:bg-white/30 transition-all duration-300 border border-white/30">
            {secondaryButtonText}
          </Link>
        </div>
      </div>
    </section>
  );
}
