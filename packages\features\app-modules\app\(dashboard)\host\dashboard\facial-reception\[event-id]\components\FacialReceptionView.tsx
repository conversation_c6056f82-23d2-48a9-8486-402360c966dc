"use client";

import { ArrowLeft } from "lucide-react";
import { QRCodeDialog } from "./QRCodeDialog";
import { CameraView } from "./views/CameraView";
import { ProcessingView } from "./views/ProcessingView";
import { ConfirmationView } from "./views/ConfirmationView";
import { SearchView } from "./views/SearchView";
import { FaceNotFoundView } from "./views/FaceNotFoundView";
import { PermissionView } from "./views/PermissionView";
import { useFacialReceptionContext } from "../context/FacialReceptionContext";

export function FacialReceptionView() {
  const { state, participantEventUrl, handleNavigateBack, handleCloseQRCodeDialogAndContinue } =
    useFacialReceptionContext();

  const renderCurrentView = () => {
    switch (state.status) {
      case "IDLE":
        return <CameraView />;
      case "DETECTING":
      case "CONFIRMING":
        return <ProcessingView />;
      case "MATCHES_FOUND":
        return <ConfirmationView />;
      case "FACE_NOT_FOUND":
        return <FaceNotFoundView />;
      case "SEARCHING":
        return <SearchView />;
      case "WAITING_PERMISSION":
        return <PermissionView />;
      case "QR_CODE_DISPLAY":
        // While showing QR code, do not render the camera component to stop video capture.
        return null;
      default:
        // Render camera view by default or a specific error component
        return <CameraView />;
    }
  };

  return (
    <div className="min-h-[100dvh] bg-slate-50/50 flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b border-slate-200/60 px-4 py-4 sm:px-6">
        <div className="max-w-4xl mx-auto flex items-center">
          <button
            onClick={handleNavigateBack}
            className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-all duration-200">
            <ArrowLeft className="w-4 h-4" />
            Back
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 px-4 py-6 sm:px-6 sm:py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-center">{renderCurrentView()}</div>
        </div>
      </div>

      {/* QR Code Dialog */}
      <QRCodeDialog
        open={state.status === "QR_CODE_DISPLAY"}
        onOpenChange={(open) => !open && handleCloseQRCodeDialogAndContinue()}
        userName={state.qrCodeDialogData?.userName}
        qrCodeValue={state.qrCodeDialogData?.url || ""}
        eventDashboardUrl={participantEventUrl}
        onContinue={handleCloseQRCodeDialogAndContinue}
      />
    </div>
  );
}
