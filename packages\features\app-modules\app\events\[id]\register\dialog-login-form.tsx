"use client";

import { useState, useCallback } from "react";
import { useTranslations } from "next-intl";

import { useToast } from "@meeeetup/ui/toaster"; // Handled by hooks
import { LoginFormFields } from "@/components/login-form-fields"; // LoginFormData also comes from useLoginForm now

// Import the new hooks
import { useLoginForm, LoginFormData } from "@/hooks/useLoginForm";
import { useGoogleLogin } from "@/hooks/useGoogleLogin";

interface DialogLoginFormProps {
  nextUrl: string;
  signupUrl: string;
  onLoginSuccess?: () => void; // This specific callback is still needed for the dialog
}

export function DialogLoginForm({ nextUrl, signupUrl, onLoginSuccess }: DialogLoginFormProps) {
  const { toast } = useToast();
  const t = useTranslations("LoginPage");
  const dialogT = useTranslations("LoginDialog");

  const displayLoginError = useCallback(
    (error?: string) => {
      const errorMessage =
        error === "CredentialsSignin"
          ? dialogT("Invalid email or password")
          : error || dialogT("unexpectedError");
      toast({
        title: "Login Failed",
        description: errorMessage,
        variant: "destructive",
      });
    },
    [dialogT]
  );

  // Use the new login form hook
  const { register, handleSubmit, errors, onSubmit, isPending } = useLoginForm({
    loginType: "participant", // Dialog is always for participants
    nextUrl,
    onLoginSuccess, // Pass the dialog-specific success callback
    onLoginError: displayLoginError, // Pass the dialog-specific error handler
    defaultLoginError: dialogT("unexpectedError"),
  });

  // Use the new Google login hook
  const { googleLoading, handleGoogleLogin, setGoogleLoading } = useGoogleLogin({
    onLoginError: displayLoginError, // Pass the dialog-specific error handler for Google login
    defaultLoginError: dialogT("googleLoginFailed"),
    // onLoginSuccess for Google can also be handled if needed,
    // but social logins often redirect, potentially making onLoginSuccess here less relevant
    // or could be tied to the main onLoginSuccess prop of DialogLoginForm if social login success also needs to trigger it.
  });

  const onGoogleLogin = useCallback(async () => {
    // setGoogleLoading(true); // Handled by useGoogleLogin
    await handleGoogleLogin("participant", nextUrl);
    // setGoogleLoading(false); // Handled by useGoogleLogin
  }, [handleGoogleLogin, nextUrl]);

  return (
    <LoginFormFields
      loginType="participant"
      handleGoogleLogin={onGoogleLogin}
      onSubmit={onSubmit}
      register={register}
      handleSubmit={handleSubmit}
      errors={errors}
      isPending={isPending}
      googleLoading={googleLoading}
      t={t}
      signupUrl={signupUrl}
    />
  );
}
