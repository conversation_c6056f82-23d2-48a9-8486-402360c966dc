"use client";

// External deps
import {
  Camera as CameraIcon,
  AlertCircle,
  RefreshCw,
  Loader2,
  SwitchCamera,
  FlipHorizontal2,
  Pause,
  Play,
} from "lucide-react";
import { Button } from "../button/Button";

// Internal logic extracted to a hook ✨
import { useFacialCapture } from "./hooks/useFacialCapture";

interface FacialReceptionCaptureProps {
  onCapture?: (image: string) => void;
  facingMode?: "user" | "environment";
  initialImage?: string;
  containerHeight?: number | string;
  className?: string;
  autoCapture?: boolean;
  minDetectionConfidence?: number;
  consecutiveFramesForCapture?: number;
  preparationTimeMs?: number;
  t?: (key: string, params?: Record<string, any>) => string;
}

function FacialReceptionCapture({
  onCapture,
  facingMode = "user",
  initialImage,
  containerHeight,
  className = "",
  autoCapture = true,
  minDetectionConfidence = 0.9,
  consecutiveFramesForCapture = 6,
  preparationTimeMs = 3000,
  t,
}: FacialReceptionCaptureProps) {
  // 🔥 All the heavy lifting now happens inside this hook.
  const {
    videoRef,
    canvasRef,
    error,
    isLoading,
    isCapturing,
    isSwitchingCamera,
    currentFacingMode,
    isFlipped,
    isPreparationPhase,
    preparationCountdown,
    isPreparationPaused,
    captureImage,
    toggleCamera,
    toggleFlip,
    skipPreparation,
    pausePreparation,
    resumePreparation,
  } = useFacialCapture({
    onCapture,
    facingMode,
    initialImage,
    autoCapture,
    minDetectionConfidence,
    consecutiveFramesForCapture,
    preparationTimeMs,
  });

  const containerStyle = containerHeight
    ? { height: typeof containerHeight === "number" ? `${containerHeight}px` : containerHeight }
    : {};

  return (
    <div className={`flex flex-col items-center gap-6 ${className}`}>
      <div
        className={`relative w-full max-w-4xl overflow-hidden rounded-3xl bg-slate-900 border border-slate-800 shadow-2xl backdrop-blur-sm ${!containerHeight ? "h-[500px] sm:h-[600px] md:h-[700px]" : ""}`}
        style={containerStyle}>
        {/* Loading/Switching Overlay */}
        {(isLoading || isSwitchingCamera) && (
          <div className="absolute inset-0 z-20 flex items-center justify-center bg-slate-900/80 backdrop-blur-md transition-all duration-500">
            <div className="flex flex-col items-center gap-4">
              <div className="relative">
                <div className="w-16 h-16 border-4 border-slate-700 rounded-full animate-pulse"></div>
                <Loader2 className="absolute inset-0 w-16 h-16 animate-spin text-blue-400 p-3" />
              </div>
              <p className="text-slate-300 text-sm font-medium tracking-wide">
                {isSwitchingCamera
                  ? t
                    ? t("FacialReceptionCapture.switchingCamera")
                    : "Switching camera..."
                  : t
                    ? t("FacialReceptionCapture.initializingCamera")
                    : "Initializing camera..."}
              </p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error ? (
          <div className="absolute inset-0 flex flex-col items-center justify-center p-8 text-center">
            <div className="p-6 rounded-2xl bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
              <AlertCircle className="w-12 h-12 mb-4 text-red-400 mx-auto" />
              <h3 className="text-lg font-semibold text-red-300 mb-2">
                {t ? t("FacialReceptionCapture.cameraAccessRequired") : "Camera Access Required"}
              </h3>
              <p className="text-red-200/80 text-sm mb-6 max-w-sm">{error}</p>
              <Button
                onClick={toggleCamera}
                className="bg-red-500 hover:bg-red-600 text-white border-0 rounded-xl px-6 py-3 font-medium transition-all duration-200 hover:scale-105 active:scale-95">
                <RefreshCw className="w-4 h-4 mr-2" />
                {t ? t("FacialReceptionCapture.tryAgain") : "Try Again"}
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* Video Element */}
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className={`w-full h-full object-cover transition-all duration-500 ${
                isSwitchingCamera ? "opacity-40 scale-105" : "opacity-100 scale-100"
              } ${isFlipped ? "scale-x-[-1]" : ""}`}
              controls={false}
            />
            <canvas ref={canvasRef} className="hidden" />

            {/* Top Controls */}
            <div className="absolute top-6 right-6 flex gap-3 z-30">
              <button
                onClick={toggleFlip}
                disabled={isLoading || isSwitchingCamera}
                className="group relative w-12 h-12 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-110 active:scale-95">
                <FlipHorizontal2 className="w-5 h-5 text-white absolute inset-0 m-auto transition-transform group-hover:rotate-180" />
                <div className="absolute inset-0 rounded-2xl bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </button>

              <button
                onClick={toggleCamera}
                disabled={isSwitchingCamera || isLoading}
                className="group relative w-12 h-12 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-110 active:scale-95">
                <SwitchCamera className="w-5 h-5 text-white absolute inset-0 m-auto transition-transform group-hover:rotate-180" />
                <div className="absolute inset-0 rounded-2xl bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </button>
            </div>

            {/* Capture Button */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
              <button
                onClick={captureImage}
                disabled={isCapturing || isLoading || isSwitchingCamera}
                className="group relative w-20 h-20 rounded-full bg-white/15 backdrop-blur-md border-4 border-white/30 hover:border-blue-400/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-110 active:scale-95 shadow-2xl">
                {/* Outer Ring Animation */}
                <div className="absolute inset-0 rounded-full border-2 border-blue-400/30 animate-ping opacity-75"></div>

                {/* Inner Content */}
                <div className="absolute inset-2 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center transition-all duration-300 group-hover:bg-blue-500/30">
                  {isCapturing ? (
                    <Loader2 className="w-8 h-8 text-white animate-spin" />
                  ) : (
                    <CameraIcon className="w-8 h-8 text-white transition-transform group-hover:scale-110" />
                  )}
                </div>

                {/* Capture Flash Effect */}
                {isCapturing && <div className="absolute inset-0 rounded-full bg-white animate-pulse"></div>}
              </button>

              {/* Capture Label */}
              <p className="text-white/80 text-xs font-medium mt-3 text-center tracking-wide">
                {t ? t("FacialReceptionCapture.tapToCapture") : "Tap to capture"}
              </p>
            </div>

            {/* Camera Mode Indicator */}
            <div className="absolute top-6 left-6 z-30">
              <div className="px-3 py-1.5 rounded-full bg-black/40 backdrop-blur-md border border-white/20">
                <span className="text-white/90 text-xs font-medium tracking-wide capitalize">
                  {currentFacingMode === "user"
                    ? t
                      ? t("FacialReceptionCapture.frontCamera")
                      : "Front"
                    : t
                      ? t("FacialReceptionCapture.backCamera")
                      : "Back"}{" "}
                  {t ? t("FacialReceptionCapture.camera") : "Camera"}
                </span>
              </div>
            </div>

            {/* Focus Ring Animation */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 border-2 border-white/30 rounded-lg animate-pulse"></div>
            </div>

            {/* Preparation Phase Overlay */}
            {isPreparationPhase && autoCapture && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/40 backdrop-blur-sm z-10">
                <div className="text-center">
                  <div className="relative w-32 h-32 mx-auto mb-6">
                    {/* Countdown Circle */}
                    <div className="absolute inset-0 rounded-full border-4 border-white/20"></div>
                    <div
                      className={`absolute inset-0 rounded-full border-4 border-blue-400 border-t-transparent ${
                        isPreparationPaused ? "" : "animate-spin"
                      }`}
                      style={{
                        animationDuration: `${preparationCountdown}s`,
                        animationTimingFunction: "linear",
                        animationPlayState: isPreparationPaused ? "paused" : "running",
                      }}></div>

                    {/* Countdown Number */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-4xl font-bold text-white">{preparationCountdown}</span>
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold text-white mb-2">
                    {t ? t("FacialReceptionCapture.getReady") : "Get Ready!"}
                  </h3>
                  <p className="text-white/80 text-sm max-w-sm mx-auto mb-6">
                    {isPreparationPaused ? (
                      <>
                        {t ? t("FacialReceptionCapture.autoCapturePaused") : "Auto-capture is paused."}
                        <br />
                        {t
                          ? t("FacialReceptionCapture.resumeOrSkip")
                          : "Resume when ready or skip to start immediately."}
                      </>
                    ) : (
                      <>
                        {t
                          ? t("FacialReceptionCapture.autoCaptureWillStartIn", {
                              seconds: preparationCountdown,
                            })
                          : `Auto-capture will start in ${preparationCountdown} second${preparationCountdown !== 1 ? "s" : ""}.`}
                        <br />
                        {t
                          ? t("FacialReceptionCapture.adjustCameraSettings")
                          : "Adjust your camera settings now."}
                      </>
                    )}
                  </p>

                  <div className="flex gap-3">
                    <Button
                      onClick={isPreparationPaused ? resumePreparation : pausePreparation}
                      className="bg-yellow-500 hover:bg-yellow-600 text-white border-0 rounded-xl px-6 py-2 font-medium transition-all duration-200 hover:scale-105 active:scale-95 flex items-center">
                      {isPreparationPaused ? (
                        <>
                          <Play className="w-4 h-4 mr-2" />
                          {t ? t("FacialReceptionCapture.resume") : "Resume"}
                        </>
                      ) : (
                        <>
                          <Pause className="w-4 h-4 mr-2" />
                          {t ? t("FacialReceptionCapture.pause") : "Pause"}
                        </>
                      )}
                    </Button>
                    <Button
                      onClick={skipPreparation}
                      className="bg-blue-500 hover:bg-blue-600 text-white border-0 rounded-xl px-6 py-2 font-medium transition-all duration-200 hover:scale-105 active:scale-95">
                      {t ? t("FacialReceptionCapture.startAutoCaptureNow") : "Start Auto-Capture Now"}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Bottom Status */}
      <div className="flex items-center gap-2 text-slate-600">
        <div
          className={`w-2 h-2 rounded-full transition-colors duration-300 ${
            error
              ? "bg-red-400"
              : isLoading || isSwitchingCamera
                ? "bg-yellow-400 animate-pulse"
                : "bg-green-400"
          }`}></div>
        <span className="text-sm font-medium">
          {error
            ? t
              ? t("FacialReceptionCapture.cameraError")
              : "Camera Error"
            : isLoading || isSwitchingCamera
              ? t
                ? t("FacialReceptionCapture.loading")
                : "Loading..."
              : t
                ? t("FacialReceptionCapture.cameraReady")
                : "Camera Ready"}
        </span>
      </div>
    </div>
  );
}

export default FacialReceptionCapture;
