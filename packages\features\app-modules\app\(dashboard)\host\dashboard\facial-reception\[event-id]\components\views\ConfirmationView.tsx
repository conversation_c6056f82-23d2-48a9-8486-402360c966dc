"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { ConfirmationDialog } from "../ConfirmationDialog";
import { useFacialReceptionContext } from "../../context/FacialReceptionContext";

export function ConfirmationView() {
  const { state, currentUser, handleConfirmCurrentMatch, handleCancelAll, dispatch } =
    useFacialReceptionContext();

  const { potentialMatches, currentMatchIndex, status } = state;
  const totalMatches = potentialMatches?.length || 0;
  const hasMultipleMatches = totalMatches > 1;
  const isRegisteringFace = status === "REGISTRATION";

  const handleNextMatch = () => dispatch({ type: "SHOW_NEXT_MATCH" });
  const handlePreviousMatch = () => dispatch({ type: "SHOW_PREV_MATCH" });

  if (!currentUser) return null;

  return (
    <div className="w-full max-w-md">
      {hasMultipleMatches && (
        <div className="bg-blue-50 border border-blue-200 rounded-xl px-4 py-3 mb-6">
          <div className="text-sm font-medium text-blue-900 text-center">
            Match {currentMatchIndex + 1} of {totalMatches}
          </div>
        </div>
      )}

      <div className="bg-white rounded-2xl shadow-sm border border-slate-200/60 overflow-hidden">
        <ConfirmationDialog
          user={{ name: currentUser.name, imageUrl: currentUser.image, email: currentUser.email }}
          onConfirm={handleConfirmCurrentMatch}
          onDeny={handleCancelAll}
          isRegisteringFace={isRegisteringFace}
        />
      </div>

      {hasMultipleMatches && (
        <div className="flex justify-center items-center gap-3 mt-6">
          <button
            onClick={handlePreviousMatch}
            disabled={currentMatchIndex === 0}
            className="p-3 rounded-xl bg-white border border-slate-200 shadow-sm disabled:opacity-40 disabled:cursor-not-allowed hover:bg-slate-50 hover:border-slate-300 transition-all duration-200"
            aria-label="Previous Match">
            <ChevronLeft className="w-5 h-5 text-slate-600" />
          </button>
          <div className="px-4 py-2 bg-slate-100 rounded-lg text-sm font-medium text-slate-700">
            {currentMatchIndex + 1} / {totalMatches}
          </div>
          <button
            onClick={handleNextMatch}
            disabled={currentMatchIndex === totalMatches - 1}
            className="p-3 rounded-xl bg-white border border-slate-200 shadow-sm disabled:opacity-40 disabled:cursor-not-allowed hover:bg-slate-50 hover:border-slate-300 transition-all duration-200"
            aria-label="Next Match">
            <ChevronRight className="w-5 h-5 text-slate-600" />
          </button>
        </div>
      )}
    </div>
  );
}
