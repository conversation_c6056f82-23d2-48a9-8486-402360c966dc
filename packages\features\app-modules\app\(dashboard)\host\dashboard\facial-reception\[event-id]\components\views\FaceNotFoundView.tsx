"use client";

import { UserX, Search, RotateCcw } from "lucide-react";
import { Button } from "@meeeetup/ui/button";
import { useFacialReceptionContext } from "../../context/FacialReceptionContext";
import { useTranslations } from "next-intl";

export function FaceNotFoundView() {
  const { dispatch } = useFacialReceptionContext();
  const t = useTranslations("FacialReception.FaceNotFoundView");

  const handleSearchManually = () => {
    dispatch({ type: "START_SEARCH" });
  };

  const handleTryAgain = () => {
    dispatch({ type: "RESET" });
  };

  return (
    <div className="w-full max-w-lg">
      <div className="bg-white rounded-2xl shadow-sm border border-slate-200/60 p-6 sm:p-8 text-center">
        <div className="mb-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <UserX className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-xl sm:text-2xl font-semibold text-slate-900 mb-3">{t("title")}</h3>
          <p className="text-slate-600 leading-relaxed">{t("description")}</p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <Button
            onClick={handleSearchManually}
            variant="primary"
            className="flex-1 py-3 text-sm font-medium flex items-center justify-center gap-2">
            <Search className="w-4 h-4" />
            {t("searchManuallyButton")}
          </Button>
          <Button
            onClick={handleTryAgain}
            variant="outline"
            className="flex-1 py-3 text-sm font-medium flex items-center justify-center gap-2">
            <RotateCcw className="w-4 h-4" />
            {t("tryAgainButton")}
          </Button>
        </div>
      </div>
    </div>
  );
}
