import { AddNoteType, UpdateNoteType } from "@/app/api/hosts/events/[id]/participants/[pId]/notes/schema";
import {
  type GET as GET_NOTES,
  POST as POST_NOTES,
  PUT as PUT_NOTES,
} from "@/app/api/hosts/events/[id]/participants/[pId]/notes/route";
import type { JsonifyApiResponse } from "@/types/api";
import { AppError } from "@/lib/errors";
import { Note } from "@meeeetup/prisma";

export type GetNotesApiResponse = JsonifyApiResponse<typeof GET_NOTES>;
export type PostNotesApiResponse = JsonifyApiResponse<typeof POST_NOTES>;
export type PostNotesApiResponseData = (PostNotesApiResponse & { success: true })["data"];
export type PutNotesApiResponse = JsonifyApiResponse<typeof PUT_NOTES>;
export type PutNotesApiResponseData = (PutNotesApiResponse & { success: true })["data"];
export type GetNoteApiResponseData = (GetNotesApiResponse & { success: true })["data"][number];

const NOTES_API_BASE = process.env.NEXT_PUBLIC_BASE_URL + "/api/hosts/events";

export async function getNotesApi(eventId: string, participantId: string): Promise<GetNotesApiResponse> {
  const url = `/api/hosts/events/${eventId}/participants/${participantId}/notes`;
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  const responseData: GetNotesApiResponse = await response.json();

  if (!response.ok && !responseData.success) {
    const error = responseData.error;
    let errorMessage = error.details || error.message;
    throw new Error(errorMessage, { cause: error.code });
  }

  return responseData as GetNotesApiResponse;
}

export async function addNoteApi(payload: AddNoteType): Promise<PostNotesApiResponse> {
  const { eventId, participantId } = payload;
  const url = `${NOTES_API_BASE}/${eventId}/participants/${participantId}/notes`;

  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  });

  const responseData: PostNotesApiResponse = await response.json();

  if (!response.ok && !responseData.success) {
    const error = responseData.error;
    let errorMessage = error.details || error.message;
    throw new Error(errorMessage, { cause: error.code });
  }

  return responseData;
}

export async function updateNoteApi(payload: UpdateNoteType): Promise<PutNotesApiResponse> {
  const { eventId, participantId } = payload;
  const url = `${NOTES_API_BASE}/${eventId}/participants/${participantId}/notes`;

  const response = await fetch(url, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  });

  const responseData: PutNotesApiResponse = await response.json();

  if (!response.ok && !responseData.success) {
    const error = responseData.error;
    let errorMessage = error.details || error.message;
    throw new Error(errorMessage);
  }

  return responseData;
}
