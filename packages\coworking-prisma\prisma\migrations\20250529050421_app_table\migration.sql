-- AlterTable
ALTER TABLE "events" ADD COLUMN     "appId" TEXT;

-- AlterTable
ALTER TABLE "policy_and_terms" ADD COLUMN     "appId" TEXT;

-- CreateTable
CREATE TABLE "apps" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "apps_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "apps_name_key" ON "apps"("name");

-- CreateIndex
CREATE INDEX "events_appId_idx" ON "events"("appId");

-- CreateIndex
CREATE INDEX "policy_and_terms_appId_idx" ON "policy_and_terms"("appId");

-- AddForeignKey
ALTER TABLE "events" ADD CONSTRAINT "events_appId_fkey" FOREIGN KEY ("appId") REFERENCES "apps"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "policy_and_terms" ADD CONSTRAINT "policy_and_terms_appId_fkey" FOREIGN KEY ("appId") REFERENCES "apps"("id") ON DELETE SET NULL ON UPDATE CASCADE;
